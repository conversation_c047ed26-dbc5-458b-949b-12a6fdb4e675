import React, { useState, useEffect } from 'react';
import { warService } from '../../services/api/war.service';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { FaShieldAlt, FaCrosshairs, FaCrown, FaCoins, FaFire, FaChartLine } from 'react-icons/fa';

const RegionWarHistory = ({ regionId }) => {
  const [history, setHistory] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRegionHistory = async () => {
      if (!regionId) return;

      try {
        setLoading(true);
        const data = await warService.getRegionWarHistory(regionId);
        setHistory(data);
      } catch (error) {
        console.error('Failed to fetch region war history:', error);
        toast.error('Failed to load region war history');
      } finally {
        setLoading(false);
      }
    };

    fetchRegionHistory();
  }, [regionId]);

  if (!regionId) {
    return null;
  }

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-lg p-6 animate-pulse">
        <div className="h-8 bg-gray-700/50 rounded-xl w-3/4 mb-6"></div>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="h-24 bg-gray-700/50 rounded-xl"></div>
          <div className="h-24 bg-gray-700/50 rounded-xl"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-48 bg-gray-700/50 rounded-xl"></div>
          ))}
        </div>
        <div className="h-40 bg-gray-700/50 rounded-xl"></div>
      </div>
    );
  }

  if (!history) {
    return (
      <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-lg p-6">
        <p className="text-gray-400 text-center py-8">No war history available for this region</p>
      </div>
    );
  }

  // Calculate overall win rate
  const wins = history.conquestsWon + history.resourceWarsWon + history.revolutionsWon;
  const losses = history.conquestsLost + history.resourceWarsLost + history.revolutionsLost;
  const total = wins + losses;
  const winRate = total > 0 ? Math.round((wins / total) * 100) : 0;

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6 border-b border-gray-700 pb-4">
        <h2 className="text-2xl font-bold text-white">
          <span className="bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
            WAR HISTORY
          </span>
          <span className="block text-gray-400 text-sm font-normal mt-1">{history.regionName}</span>
        </h2>
        <div className="bg-gray-700/50 p-2 rounded-lg">
          <FaChartLine className="text-red-500 text-xl" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <div className="bg-red-900/30 p-2 rounded-lg mr-3">
                <FaCrosshairs className="text-red-400" />
              </div>
              <h3 className="text-gray-300">WARS AS ATTACKER</h3>
            </div>
            <span className="text-red-400 text-2xl">⚔️</span>
          </div>
          <p className="text-3xl font-bold text-white text-center">{history.warsAsAttacker}</p>
        </div>

        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <div className="bg-blue-900/30 p-2 rounded-lg mr-3">
                <FaShieldAlt className="text-blue-400" />
              </div>
              <h3 className="text-gray-300">WARS AS DEFENDER</h3>
            </div>
            <span className="text-blue-400 text-2xl">🛡️</span>
          </div>
          <p className="text-3xl font-bold text-white text-center">{history.warsAsDefender}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-purple-900/30 p-2 rounded-lg mr-3">
                <FaCrown className="text-purple-400" />
              </div>
              <h3 className="text-gray-300">CONQUEST WARS</h3>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400 font-bold text-xl">{history.conquestsWon}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400 font-bold text-xl">{history.conquestsLost}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg border-t border-gray-700 mt-3 pt-3">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white font-bold text-xl">
                {history.conquestsWon + history.conquestsLost > 0
                  ? `${Math.round((history.conquestsWon / (history.conquestsWon + history.conquestsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-yellow-900/30 p-2 rounded-lg mr-3">
                <FaCoins className="text-yellow-400" />
              </div>
              <h3 className="text-gray-300">RESOURCE WARS</h3>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400 font-bold text-xl">{history.resourceWarsWon}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400 font-bold text-xl">{history.resourceWarsLost}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg border-t border-gray-700 mt-3 pt-3">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white font-bold text-xl">
                {history.resourceWarsWon + history.resourceWarsLost > 0
                  ? `${Math.round((history.resourceWarsWon / (history.resourceWarsWon + history.resourceWarsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="bg-red-900/30 p-2 rounded-lg mr-3">
                <FaFire className="text-red-400" />
              </div>
              <h3 className="text-gray-300">REVOLUTIONS</h3>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Won:</span>
              <span className="text-green-400 font-bold text-xl">{history.revolutionsWon}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg">
              <span className="text-gray-400">Lost:</span>
              <span className="text-red-400 font-bold text-xl">{history.revolutionsLost}</span>
            </div>
            <div className="flex justify-between items-center bg-gray-800/50 p-3 rounded-lg border-t border-gray-700 mt-3 pt-3">
              <span className="text-gray-400">Win Rate:</span>
              <span className="text-white font-bold text-xl">
                {history.revolutionsWon + history.revolutionsLost > 0
                  ? `${Math.round((history.revolutionsWon / (history.revolutionsWon + history.revolutionsLost)) * 100)}%`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <div className="flex items-center justify-between mb-4 border-b border-gray-700 pb-3">
          <h3 className="text-lg font-bold text-white flex items-center">
            <FaChartLine className="text-neonBlue mr-2" />
            OVERALL PERFORMANCE
          </h3>
        </div>
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex justify-between items-center bg-gray-800/50 p-4 rounded-xl">
                <span className="text-gray-400">Total Wars:</span>
                <span className="text-white font-bold text-xl">
                  {history.warsAsAttacker + history.warsAsDefender}
                </span>
              </div>
              
              <div className="flex justify-between items-center bg-gray-800/50 p-4 rounded-xl">
                <span className="text-gray-400">Total Victories:</span>
                <span className="text-green-400 font-bold text-xl">
                  {wins}
                </span>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center bg-gray-800/50 p-4 rounded-xl">
                <span className="text-gray-400">Total Defeats:</span>
                <span className="text-red-400 font-bold text-xl">
                  {losses}
                </span>
              </div>
              
              <div className="flex justify-between items-center bg-gray-800/50 p-4 rounded-xl">
                <span className="text-gray-400">Win Rate:</span>
                <span className="text-white font-bold text-xl">
                  {total > 0 ? `${winRate}%` : 'N/A'}
                </span>
              </div>
            </div>
          </div>
          
          {total > 0 && (
            <div className="mt-6">
              <div className="flex justify-between text-xs mb-1">
                <span className="text-gray-400">DEFEATS: {losses}</span>
                <span className="text-gray-400">VICTORIES: {wins}</span>
              </div>
              <div className="w-full bg-gray-700 h-3 rounded-full overflow-hidden border border-gray-600">
                <div 
                  className="bg-gradient-to-r from-red-600 to-green-600 h-full"
                  style={{ width: `${winRate}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs mt-1">
                <span className="text-red-400">0%</span>
                <span className="text-gray-400">50%</span>
                <span className="text-green-400">100%</span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mt-8 text-center">
        <Link 
          to="/wars" 
          className="inline-block px-6 py-3 rounded-xl font-bold transition-all bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-500 hover:to-blue-700 text-white border-2 border-blue-700 shadow-lg shadow-blue-900/30"
        >
          VIEW ALL WARS
        </Link>
      </div>
    </div>
  );
};

export default RegionWarHistory;