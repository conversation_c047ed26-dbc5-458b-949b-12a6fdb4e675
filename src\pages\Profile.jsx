import React, { useState, useEffect, useRef } from "react";
import Navbar from "../components/Navbar";
import api from "../services/api/api";
import { useAuthGuard } from "../hooks/useAuthGuard";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { useCountdown } from "../hooks/useCountdown";
import { calculateTrainingTime } from "../utils/calculateTrainingTime";
import { calculateTrainingCost } from "../utils/calculateTrainingCost";
import useAuthStore from "../store/useAuthStore";
import useUserDataStore from "../store/useUserDataStore";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import CreateStateModal from "../components/states/CreateStateModal";
import CreatePartyModal from "../components/parties/CreatePartyModal";
import UserWarStats from "../components/analytics/UserWarStats.tsx";
import { useUserResources } from "../hooks/useUserResources";
import BalanceLogsModal from "../components/balance/BalanceLogsModal";
import {
  FaCrown,
  FaCoins,
  FaEdit,
  FaCheck,
  FaTimes,
  FaShare,
  FaCopy,
  FaCamera,
  FaTrash,
  FaUser,
  FaDumbbell,
  FaShieldAlt,
  FaBrain,
  FaGlobeAmericas,
  FaUsers,
  FaFlag,
  FaFireAlt,
  FaChartLine,
  FaLevelUpAlt,
  FaDollarSign,
  FaMedal,
  FaLandmark,
  FaTrophy,
} from "react-icons/fa";
import { userService } from "../services/api/user.service";
import { stateService } from "../services/api/state.service";
import { warService } from "../services/api/war.service";
import Footer from "../components/common/Footer";

export default function Profile() {
  useAuthGuard();
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const {
    userData: profile,
    loading: userDataLoading,
    fetchUserData,
  } = useUserDataStore();
  const [loading, setLoading] = useState(true);
  const [region, setRegion] = useState(null);
  const [countdown, setCountdown] = useState(null);
  const [myState, setMyState] = useState(null);
  const [myWars, setMyWars] = useState([]);
  const trainingCompletedShown = useRef(false);
  const [isCreateStateModalOpen, setIsCreateStateModalOpen] = useState(false);
  const [isCreatePartyModalOpen, setIsCreatePartyModalOpen] = useState(false);
  const [isBalanceLogsModalOpen, setIsBalanceLogsModalOpen] = useState(false);
  const { refetch: refetchResources } = useUserResources();
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [newUsername, setNewUsername] = useState("");
  const [isUpdatingUsername, setIsUpdatingUsername] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [isDeletingAvatar, setIsDeletingAvatar] = useState(false);
  const fileInputRef = useRef(null);

  const isTraining = !!countdown;

  const xpForCurrentLevel = Math.pow(profile?.level, 2) * 100;
  const xpForNextLevel = Math.pow(profile?.level + 1, 2) * 100;

  const xpIntoLevel = profile?.experience - xpForCurrentLevel;
  const xpNeeded = xpForNextLevel - xpForCurrentLevel;

  const levelBar = Math.floor((xpIntoLevel / xpNeeded) * 100);

  useEffect(() => {
    if (!user?.id) return;

    const fetchData = async () => {
      try {
        // Fetch user data using the centralized store (use cache if available)
        await fetchUserData(false);

        if (profile?.region) {
          setRegion(profile.region);
        }
        // Fetch state data using dedicated endpoint
        try {
          const stateRes = await stateService.getUserState();

          setMyState(stateRes);
        } catch (error) {
          // User doesn't have a state, which is fine
          setMyState(null);
        }

        // Fetch wars data
        let myWars = [];
        try {
          myWars = await warService.getMyWars();
        } catch (error) {
          // User doesn't have wars, which is fine
        }
        setMyWars(myWars);

        // Check if there's active training and set countdown
        if (profile?.trainingExpiresAt) {
          const now = Date.now();
          const target = new Date(profile.trainingExpiresAt).getTime();
          if (target > now) {
            // Only set up countdown for future expiry dates
            const diff = target - now;
            const hours = Math.floor(diff / 1000 / 60 / 60);
            const minutes = Math.floor((diff / 1000 / 60) % 60);
            const seconds = Math.floor((diff / 1000) % 60);

            if (hours === 0 && minutes === 0) {
              setCountdown(`${seconds}s`);
            } else if (hours === 0) {
              setCountdown(`${minutes}m ${seconds}s`);
            } else {
              setCountdown(`${hours}h ${minutes}m ${seconds}s`);
            }
          }
        }
      } catch (error) {
        showErrorToast(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.id]); // Keep using the correct dependency

  // This useEffect handles the countdown timer and completion
  useEffect(() => {
    if (!profile?.trainingExpiresAt) return;

    // Check if the training is already expired
    const now = Date.now();
    const target = new Date(profile.trainingExpiresAt).getTime();

    // If training has already expired, don't set up the countdown
    if (target <= now) return;

    let intervalId;
    const isActiveTraining = true; // Flag to track this specific training session

    // We no longer need to calculate progress or duration since we're not updating it
    // in the interval to avoid re-renders

    const updateCountdown = () => {
      const currentTime = Date.now();
      const timeRemaining = target - currentTime;

      if (timeRemaining <= 0) {
        setCountdown(null);
        clearInterval(intervalId);

        // Only show completion toast if this was an active training session
        // that completed during this component's lifecycle
        if (isActiveTraining) {
          showSuccessToast("Training completed!");
          refetchProfile();
        }
      } else {
        const hours = Math.floor(timeRemaining / 1000 / 60 / 60);
        const minutes = Math.floor((timeRemaining / 1000 / 60) % 60);
        const seconds = Math.floor((timeRemaining / 1000) % 60);

        // Special formatting when less than a minute remains
        if (hours === 0 && minutes === 0) {
          setCountdown(`${seconds}s`);
        } else if (hours === 0) {
          setCountdown(`${minutes}m ${seconds}s`);
        } else {
          setCountdown(`${hours}h ${minutes}m ${seconds}s`);
        }

        // We don't update the store here anymore to prevent re-renders
        // The progress bar will still show the initial progress value
        // and will be updated when the training completes via refetchProfile()
      }
    };

    updateCountdown();
    intervalId = setInterval(updateCountdown, 1000);

    return () => {
      clearInterval(intervalId);
    };
    // Only run this effect when trainingExpiresAt changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profile?.trainingExpiresAt]);

  const refetchProfile = async () => {
    try {
      // Force refresh when explicitly called
      await fetchUserData(true);
    } catch (error) {
      showErrorToast("Failed to refresh profile.");
    }
  };

  // Username editing functions
  const handleEditUsername = () => {
    setNewUsername(profile?.username || "");
    setIsEditingUsername(true);
  };

  const handleCancelEdit = () => {
    setIsEditingUsername(false);
    setNewUsername("");
  };

  const handleSaveUsername = async () => {
    if (!newUsername.trim()) {
      showErrorToast("Username cannot be empty");
      return;
    }

    if (newUsername === profile?.username) {
      setIsEditingUsername(false);
      return;
    }

    setIsUpdatingUsername(true);
    try {
      await userService.updateProfile({
        username: newUsername.trim(),
        id: profile.id,
      });
      showSuccessToast("Username updated successfully!");

      // Force refresh the user data to reflect the change
      await fetchUserData(true);

      setIsEditingUsername(false);
      setNewUsername("");
    } catch (error) {
      showErrorToast(error || "Failed to update username");
    } finally {
      setIsUpdatingUsername(false);
    }
  };

  const handleUsernameKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSaveUsername();
    } else if (e.key === "Escape") {
      handleCancelEdit();
    }
  };

  // Share profile functions
  const getShareableProfileUrl = () => {
    return `${window.location.origin}/users/${profile?.id}`;
  };

  const handleCopyProfileUrl = async () => {
    try {
      const url = getShareableProfileUrl();
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      showSuccessToast("Profile URL copied to clipboard!");
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      showErrorToast("Failed to copy URL to clipboard");
    }
  };

  const handleShareProfile = () => {
    setShowShareModal(true);
  };

  // Avatar upload functions
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Client-side validation
    const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      alert("Please select a JPEG, PNG, or WebP image file.");
      return;
    }

    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      alert("File size must be less than 5MB.");
      return;
    }

    setIsUploadingAvatar(true);

    try {
      const result = await userService.uploadAvatar(file);

      // Update profile with new avatar URL
      profile.avatarUrl = result.avatarUrl;

      console.log("Avatar uploaded successfully:", result);
    } catch (error) {
      console.error("Error uploading avatar:", error);
      alert(error.message || "Failed to upload avatar. Please try again.");
    } finally {
      setIsUploadingAvatar(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleDeleteAvatar = async () => {
    if (!profile?.avatarUrl) return;

    const confirmed = window.confirm(
      "Are you sure you want to delete your profile picture?"
    );
    if (!confirmed) return;

    setIsDeletingAvatar(true);

    try {
      await userService.deleteAvatar();

      // Remove avatar URL from profile
      setProfile((prev) => ({
        ...prev,
        avatarUrl: null,
      }));

      console.log("Avatar deleted successfully");
    } catch (error) {
      console.error("Error deleting avatar:", error);
      alert(error.message || "Failed to delete avatar. Please try again.");
    } finally {
      setIsDeletingAvatar(false);
    }
  };

  function formatTime(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    return `${hours}h ${minutes}m ${seconds}s`;
  }

  const handleTrain = async (perkType, durationMinutes, currency, amount) => {
    try {
      await api.post("/users/train", {
        trainingType: perkType,
        duration: durationMinutes,
        currency,
        amount,
      });

      showSuccessToast(`Started training ${perkType}`);

      // Force a refresh of user data since we know it changed
      await fetchUserData(true);

      // This will use the already fetched user data
      refetchResources();
    } catch (error) {
      showErrorToast(error);
    }
  };

  // We use refreshStateData instead of this function

  // Function to refresh state data
  const refreshStateData = async () => {
    try {
      const stateRes = await stateService.getUserState();
      setMyState(stateRes);
    } catch (error) {
      setMyState(null);
    }
  };

  // Function to refresh party data
  const refreshPartyData = async () => {
    try {
      // Force refresh when explicitly called
      await fetchUserData(true);
    } catch (error) {
      console.error("Error refreshing profile data:", error);
    }
  };

  if (loading || userDataLoading || !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-blue-400 text-xl animate-pulse">
            Loading profile...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="bg-gray-800/70 backdrop-blur-md rounded-xl shadow-2xl p-6 mb-8 border border-gray-700/50 relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-48 h-48 bg-blue-600/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>

          <div className="flex flex-col md:flex-row items-center md:items-start">
            {/* Avatar Section */}
            <div className="relative mb-4 md:mb-0">
              <div className="w-28 h-28 bg-gradient-to-br from-blue-900/30 to-indigo-900/30 rounded-full flex items-center justify-center text-4xl text-blue-400 overflow-hidden relative group cursor-pointer border-2 border-blue-500/30 shadow-lg">
                {profile?.avatarUrl ? (
                  <img
                    src={profile.avatarUrl}
                    alt="Profile Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <span className="text-4xl font-bold">
                    {profile?.username?.charAt(0).toUpperCase() || "U"}
                  </span>
                )}

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={handleAvatarClick}
                    disabled={isUploadingAvatar || isDeletingAvatar}
                    className="text-white p-3 rounded-full bg-blue-600/80 hover:bg-blue-700 transition-all"
                    title="Upload avatar"
                  >
                    <FaCamera className="text-xl" />
                  </button>
                </div>

                {/* Upload overlay when uploading */}
                {isUploadingAvatar && (
                  <div className="absolute inset-0 bg-black/70 rounded-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/webp"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
            <div className="md:ml-6 text-center md:text-left flex-grow">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                <div>
                  {/* Username with edit functionality */}
                  <div className="flex items-center justify-center md:justify-start mb-2">
                    {isEditingUsername ? (
                      <div className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={newUsername}
                          onChange={(e) => setNewUsername(e.target.value)}
                          onKeyDown={handleUsernameKeyPress}
                          className="w-30 font-bold bg-gray-700/50 text-white px-4 py-2 rounded-xl border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/30 focus:outline-none"
                          placeholder="Enter username"
                          maxLength={20}
                          disabled={isUpdatingUsername}
                        />
                        <button
                          onClick={handleSaveUsername}
                          disabled={isUpdatingUsername}
                          className="bg-green-600 hover:bg-green-700 text-white p-2 rounded-full disabled:opacity-50 transition-colors"
                          title="Save username"
                        >
                          <FaCheck />
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          disabled={isUpdatingUsername}
                          className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-full disabled:opacity-50 transition-colors"
                          title="Cancel edit"
                        >
                          <FaTimes />
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                          {profile?.username}
                        </h1>
                        <button
                          onClick={handleEditUsername}
                          className="text-gray-400 hover:text-blue-400 p-1 ml-2 transition-colors"
                          title="Edit username"
                        >
                          <FaEdit />
                        </button>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm mt-1">
                    Member since{" "}
                    {new Date(profile?.createdAt).toLocaleDateString()}
                  </p>
                </div>

                {/* Premium Badge or Get Premium Button and Share Button */}
                <div className="mt-2 md:mt-0 flex items-center justify-center md:justify-end gap-3">
                  {profile?.isPremium ? (
                    <div
                      className={`px-4 py-2 rounded-xl flex items-center bg-gradient-to-r from-yellow-700/30 to-amber-700/30 border border-yellow-500/30 backdrop-blur-sm shadow hover:shadow-lg hover:shadow-yellow-500/10 transition-all ${
                        profile?.subscriptionStatus === "cancel_at_period_end"
                          ? "border-yellow-700 text-yellow-300"
                          : "text-yellow-400"
                      }`}
                    >
                      <FaCrown className="mr-2 text-yellow-300" />
                      <div className="flex flex-col">
                        <span className="font-medium">Premium Active</span>
                        {profile?.premiumExpiresAt && (
                          <span className="text-xs">
                            Expires:{" "}
                            {new Date(
                              profile.premiumExpiresAt
                            ).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>
                  ) : (
                    <Link
                      to="/shop"
                      className="bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-yellow-500/20"
                    >
                      <FaCrown className="mr-2 text-yellow-300" />
                      <span>Get Premium</span>
                    </Link>
                  )}

                  {/* Share Profile Button */}
                  <button
                    onClick={handleShareProfile}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                    title="Share your profile"
                  >
                    <FaShare className="mr-2" />
                    <span>Share Profile</span>
                  </button>
                </div>
              </div>

              {/* Stacked Stats Cards */}
              <div className="mt-3 flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-2">
                {/* Level Card */}
                <div className="bg-gradient-to-r from-blue-800/40 to-indigo-800/40 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-500/30 shadow-lg hover:shadow-blue-500/20 transition-all md:w-1/3">
                  <div className="flex items-center justify-center">
                    <FaLevelUpAlt className="text-blue-400 text-xl pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm md:pt-1">Level</p>
                      <p className="text-white text-2xl font-bold mb-4">
                        {profile?.level || 1}
                      </p>
                    </div>
                    <div className="absolute left-16 right-8 bottom-0 md:left-16 md:right-8 ">
                      <div className="h-2 bg-gray-600 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-300"
                          style={{ width: `${levelBar}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-400 mt-0.5 pb-1">
                        {levelBar}% to next level
                      </p>
                    </div>
                  </div>
                </div>

                {/* Money Card */}
                <div
                  className="bg-gradient-to-r from-green-800/40 to-emerald-800/40 backdrop-blur-sm rounded-xl p-4 cursor-pointer text-center border border-green-500/30 shadow-lg hover:shadow-green-500/20 transition-all md:w-1/3"
                  onClick={() => setIsBalanceLogsModalOpen(true)}
                >
                  <div className="flex items-center justify-center">
                    <FaDollarSign className="text-green-400 text-xl md:mt-4 pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm md:pt-1">Money</p>
                      <p className="text-white text-2xl font-bold">
                        <span className="text-green-400">
                          {profile?.money?.toLocaleString() || 0}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>

                {/* Gold Card */}
                <div
                  className="bg-gradient-to-r from-yellow-800/40 to-amber-800/40 backdrop-blur-sm rounded-xl p-4 cursor-pointer text-center border border-yellow-500/30 shadow-lg hover:shadow-yellow-500/20 transition-all md:w-1/3"
                  onClick={() => setIsBalanceLogsModalOpen(true)}
                >
                  <div className="flex items-center justify-center">
                    <FaCoins className="text-yellow-400 text-xl md:mt-4 pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm md:pt-1">Gold</p>
                      <p className="text-white text-2xl font-bold">
                        <span className="text-yellow-400">
                          {profile?.gold?.toLocaleString() || 0}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Training Section */}
        <div className="bg-gradient-to-r from-gray-800/60 via-gray-700/60 to-gray-800/60 backdrop-blur-md rounded-xl shadow-2xl p-6 mb-8 border border-gray-700/30 relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-purple-600/10 rounded-full blur-3xl -translate-y-1/2 -translate-x-1/2"></div>
          <div className="absolute bottom-0 right-0 w-40 h-40 bg-blue-600/10 rounded-full blur-3xl translate-y-1/2 translate-x-1/2"></div>

          <div className="flex items-center justify-center mb-6">
            <FaDumbbell className="text-blue-400 text-2xl mr-3 animate-pulse" />
            <h2 className="text-2xl font-bold text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              TRAINING CENTER
            </h2>
            {profile.isPremium && (
              <div className="ml-3 bg-gradient-to-r from-yellow-700/30 to-amber-700/30 border border-yellow-500/30 text-yellow-400 px-3 py-1 rounded-xl flex items-center text-sm backdrop-blur-sm">
                <FaCrown className="mr-1" />
                <span>Premium</span>
              </div>
            )}
          </div>

          {isTraining ? (
            <div className="max-w-2xl mx-auto text-center">
              <div className="bg-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-blue-500/30 shadow-lg transform transition-transform hover:scale-[1.01]">
                <p className="text-xl text-white mb-3">
                  Currently training:{" "}
                  <span className="font-bold text-blue-400">
                    {profile.trainingPerk}
                  </span>
                </p>
                <div className="mb-5">
                  <span className="text-gray-400">Time remaining: </span>
                  <div
                    className={`inline-flex bg-gray-800/70 backdrop-blur-sm rounded-xl px-4 py-2 font-mono font-semibold text-white mt-2 ${
                      countdown && countdown.length <= 3
                        ? "animate-pulse bg-gradient-to-r from-red-900/70 to-red-800/70"
                        : "bg-gradient-to-r from-blue-900/70 to-indigo-900/70"
                    }`}
                  >
                    {countdown &&
                      countdown.split(" ").map((segment, index) => (
                        <span
                          key={index}
                          className={`mx-1 ${
                            segment.endsWith("s") && countdown.length <= 3
                              ? "text-red-400 text-lg"
                              : segment.endsWith("s")
                              ? "text-red-400"
                              : "text-blue-400"
                          }`}
                        >
                          {segment}
                        </span>
                      ))}
                  </div>
                </div>
                <div className="w-full bg-gray-800/70 backdrop-blur-sm rounded-full h-3 mb-4">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full relative overflow-hidden transition-all duration-1000 ease-linear"
                    style={{ width: `${profile.trainingProgress || 0}%` }}
                  >
                    <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-shimmer"></div>
                  </div>
                </div>
                <p className="text-gray-300 text-sm">
                  Your skills will improve when training is complete
                </p>
              </div>
            </div>
          ) : (
            <div>
              <div className="bg-gradient-to-r from-green-900/30 to-emerald-900/30 backdrop-blur-sm rounded-xl p-4 mb-6 max-w-2xl mx-auto border border-green-500/30 shadow transform transition-transform hover:scale-[1.01]">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse mr-3"></div>
                  <span className="text-green-400 font-medium">
                    Ready to train - improve your skills now!
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {[
                  {
                    perk: "strength",
                    icon: <FaDumbbell className="text-red-400" />,
                    color: "red",
                  },
                  {
                    perk: "intelligence",
                    icon: <FaBrain className="text-blue-400" />,
                    color: "blue",
                  },
                  {
                    perk: "endurance",
                    icon: <FaShieldAlt className="text-green-400" />,
                    color: "green",
                  },
                ].map(({ perk, icon, color }) => {
                  const currentLevel = profile[perk];
                  const edu = region?.educationIndex || 0;
                  const isPremium = profile.isPremium || false;

                  const goldTime = calculateTrainingTime({
                    perkLevel: currentLevel,
                    eduResidency: edu,
                    currency: "gold",
                    isPremium,
                  });

                  const moneyTime = calculateTrainingTime({
                    perkLevel: currentLevel,
                    eduResidency: edu,
                    currency: "money",
                    isPremium,
                  });

                  const goldCost = calculateTrainingCost({
                    perkLevel: currentLevel,
                    duration: goldTime,
                  }).gold;

                  const moneyCost = calculateTrainingCost({
                    perkLevel: currentLevel,
                    duration: moneyTime,
                  }).money;

                  return (
                    <div
                      key={perk}
                      className="bg-gradient-to-b from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden shadow-xl border border-gray-700/50 hover:border-blue-500/50 transition-all transform hover:-translate-y-1 duration-300"
                    >
                      {/* Card Header */}
                      <div
                        className={`px-4 py-5 border-b border-gray-700/50 bg-gradient-to-r from-${color}-900/20 to-${color}-800/20`}
                      >
                        <div className="flex items-center justify-center">
                          <div className="text-3xl mr-3">{icon}</div>
                          <h3 className="text-2xl font-bold text-white text-center uppercase tracking-wide">
                            {perk}
                          </h3>
                        </div>
                      </div>

                      {/* Current Level */}
                      <div className="py-6 px-6 text-center border-b border-gray-700/50">
                        <div className="text-gray-400 text-sm uppercase tracking-wide mb-2">
                          Current Level
                        </div>
                        <div className="text-4xl font-bold text-white">
                          {currentLevel}
                        </div>
                      </div>

                      {/* Training Options */}
                      <div className="p-5">
                        {/* Gold Training Option */}
                        <div className="mb-5 p-4 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl border border-yellow-700/50">
                          <div className="flex justify-between mb-3">
                            <span className="text-yellow-400 font-semibold text-lg flex items-center">
                              <FaCoins className="mr-2" />
                              Gold Training
                            </span>
                            <div className="flex items-center space-x-2 text-white font-bold text-lg">
                              {goldCost.toLocaleString()}
                              <h2> </h2>
                              <FaCoins className="text-yellow-400" />
                            </div>
                          </div>
                          <div className="flex items-center text-gray-300 text-sm mb-4">
                            <span>Training time: {formatTime(goldTime)}</span>
                            {profile.isPremium && (
                              <span className="ml-2 bg-gradient-to-r from-yellow-900/30 to-amber-900/30 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
                                <FaCrown className="mr-1" size={10} />
                                50% Faster
                              </span>
                            )}
                          </div>
                          <button
                            className="w-full bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 text-white py-3 rounded-xl font-medium transition-all shadow hover:shadow-lg hover:shadow-yellow-500/20"
                            onClick={() =>
                              handleTrain(perk, goldTime, "gold", goldCost)
                            }
                          >
                            Train with Gold
                          </button>
                        </div>

                        {/* Money Training Option */}
                        <div className="p-4 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl border border-green-700/50">
                          <div className="flex justify-between mb-3">
                            <span className="text-green-400 font-semibold text-lg flex items-center">
                              <FaDollarSign className="mr-2" />
                              Money Training
                            </span>
                            <span className="text-white font-bold text-lg">
                              {moneyCost.toLocaleString()} 💰
                            </span>
                          </div>
                          <div className="flex items-center text-gray-300 text-sm mb-4">
                            <span>Training time: {formatTime(moneyTime)}</span>
                            {profile.isPremium && (
                              <span className="ml-2 bg-gradient-to-r from-yellow-900/30 to-amber-900/30 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
                                <FaCrown className="mr-1" size={10} />
                                50% Faster
                              </span>
                            )}
                          </div>
                          <button
                            className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-3 rounded-xl font-medium transition-all shadow hover:shadow-lg hover:shadow-green-500/20"
                            onClick={() =>
                              handleTrain(perk, moneyTime, "money", moneyCost)
                            }
                          >
                            Train with Money
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Other Information - 2 Columns Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* State Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            {/* Decorative Element */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-red-600/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>

            <div className="flex items-center mb-4">
              <FaFlag className="text-red-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">My State</h2>
            </div>

            {myState ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-900/30 to-rose-900/30 rounded-xl flex items-center justify-center text-2xl text-red-400 border border-red-500/30">
                    {myState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {myState.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {myState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaGlobeAmericas className="mr-2 text-blue-400" />
                      Regions:
                    </span>
                    <span className="text-white">
                      {myState.regions?.length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaLandmark className="mr-2 text-yellow-400" />
                      Treasury:
                    </span>
                    <span className="text-yellow-400 flex items-center">
                      {myState.treasury?.toLocaleString() || 0}
                      <FaCoins className="ml-1.5 text-yellow-300" />
                    </span>
                  </div>
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaChartLine className="mr-2 text-green-400" />
                      Status:
                    </span>
                    <span
                      className={`${
                        myState.isActive ? "text-green-400" : "text-red-400"
                      }`}
                    >
                      {myState.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/states/${myState.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View State Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-6">
                  You don't belong to any state yet.
                </p>
                <button
                  onClick={() => setIsCreateStateModalOpen(true)}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl inline-block transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  Create a State
                </button>
              </div>
            )}
          </div>

          {/* Party Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            {/* Decorative Element */}
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-purple-600/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>

            <div className="flex items-center mb-4">
              <FaUsers className="text-purple-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">My Party</h2>
            </div>

            {profile?.leadingParty ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-900/30 to-violet-900/30 rounded-xl flex items-center justify-center text-2xl text-purple-400 border border-purple-500/30">
                    {profile.leadingParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.leadingParty.name}
                    </h3>
                    <p className="text-gray-400">Leader: {profile.username}</p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaUser className="mr-2 text-blue-400" />
                      Members:
                    </span>
                    <span className="text-white">
                      {profile.leadingParty.membersCount || profile.leadingParty.members?.length || 1}
                    </span>
                  </div>
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaGlobeAmericas className="mr-2 text-emerald-400" />
                      Region:
                    </span>
                    <span className="text-white">
                      {profile.leadingParty.region?.name || "Unknown"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.leadingParty.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Party Details
                </Link>
              </div>
            ) : profile?.memberOfParty ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-900/30 to-violet-900/30 rounded-xl flex items-center justify-center text-2xl text-purple-400 border border-purple-500/30">
                    {profile.memberOfParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.memberOfParty.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader:{" "}
                      {profile.memberOfParty.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaUser className="mr-2 text-blue-400" />
                      Members:
                    </span>
                    <span className="text-white">
                      {profile.memberOfParty.membersCount || profile.memberOfParty.members?.length || 1}
                    </span>
                  </div>
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <FaGlobeAmericas className="mr-2 text-emerald-400" />
                      Region:
                    </span>
                    <span className="text-white">
                      {profile.memberOfParty.region?.name || "Unknown"}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.memberOfParty.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Party Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-6">
                  You don't belong to any party yet.
                </p>
                <button
                  onClick={() => setIsCreatePartyModalOpen(true)}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl inline-block transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  Create a Party
                </button>
                <p className="text-sm text-gray-400 mt-3 flex items-center justify-center">
                  <FaCoins className="mr-1 text-yellow-400" /> Cost: 200 gold
                </p>
              </div>
            )}
          </div>

          {/* Region Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            {/* Decorative Element */}
            <div className="absolute top-0 left-0 w-20 h-20 bg-emerald-600/10 rounded-full blur-3xl -translate-y-1/2 -translate-x-1/2"></div>

            <div className="flex items-center mb-4">
              <FaGlobeAmericas className="text-emerald-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">My Region</h2>
            </div>

            {profile?.region ? (
              <div className="space-y-4">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-900/30 to-teal-900/30 rounded-xl flex items-center justify-center text-2xl text-emerald-400 border border-emerald-500/30">
                    {profile.region.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.region.name}
                    </h3>
                    <p className="text-gray-400">
                      Population:{" "}
                      {profile.region?.population?.toLocaleString() || "N/A"}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gray-700/30 rounded-lg p-3">
                    <h4 className="text-gray-400 text-sm uppercase mb-2 flex items-center">
                      <FaCoins className="mr-2 text-yellow-400" />
                      Resources
                    </h4>
                    {profile.region.resources ? (
                      <div className="grid grid-cols-2 gap-2">
                        {Object.entries(profile.region.resources).map(
                          ([key, value]) => (
                            <div
                              key={key}
                              className="flex justify-between bg-gray-800/40 p-2 rounded"
                            >
                              <span className="text-gray-400">{key}:</span>
                              <span className="text-white">
                                {typeof value === "object"
                                  ? value.current
                                  : value}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-400 text-center">No resources</p>
                    )}
                  </div>
                </div>
                <Link
                  to={`/regions/${profile.region.id}`}
                  className="w-full mt-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Region Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  You don't have a region assigned yet.
                </p>
              </div>
            )}
          </div>

          {/* War Statistics */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 mb-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            {/* Decorative Element */}
            <div className="absolute bottom-0 right-0 w-20 h-20 bg-amber-600/10 rounded-full blur-3xl translate-y-1/2 translate-x-1/2"></div>

            <div className="flex items-center mb-4">
              <FaChartLine className="text-amber-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">
                War Statistics
              </h2>
            </div>
            <UserWarStats />
          </div>

          {/* War Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            {/* Decorative Element */}
            <div className="absolute top-0 right-0 w-20 h-20 bg-orange-600/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>

            <div className="flex items-center mb-4">
              <FaFireAlt className="text-orange-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">My Wars</h2>
            </div>

            {myWars.length > 0 ? (
              <div className="max-h-80 overflow-y-auto custom-scrollbar pr-2">
                {myWars.map((war) => (
                  <div
                    key={war.id}
                    className="border-b border-gray-700/50 pb-4 mb-4 last:border-0 last:mb-0 last:pb-0"
                  >
                    <h3 className="text-lg font-medium text-white mb-3 flex items-center">
                      <FaFireAlt className="mr-2 text-orange-400" />
                      {war.warType} War
                    </h3>
                    <div className="space-y-2 mb-3">
                      <div className="flex justify-between bg-gray-700/30 rounded-lg p-2">
                        <span className="text-gray-400 flex items-center">
                          <FaFlag className="mr-2 text-red-400" />
                          Attacker:
                        </span>
                        <p className="text-gray-300">
                          <Link
                            to={`/regions/${war.attackerRegion.id}`}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            {war.attackerRegion.name}
                          </Link>
                        </p>
                      </div>
                      <div className="flex justify-between bg-gray-700/30 rounded-lg p-2">
                        <span className="text-gray-400 flex items-center">
                          <FaShieldAlt className="mr-2 text-green-400" />
                          Defender:
                        </span>
                        <p className="text-gray-300">
                          <Link
                            to={`/regions/${war.defenderRegion.id}`}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            {war.defenderRegion.name}
                          </Link>
                        </p>
                      </div>
                      <div className="flex justify-between bg-gray-700/30 rounded-lg p-2">
                        <span className="text-gray-400 flex items-center">
                          <FaChartLine className="mr-2 text-blue-400" />
                          Status:
                        </span>
                        <span
                          className={`${
                            war.status === "active"
                              ? "text-green-400"
                              : "text-gray-400"
                          }`}
                        >
                          {war.status}
                        </span>
                      </div>
                      <div className="flex justify-between bg-gray-700/30 rounded-lg p-2">
                        <span className="text-gray-400 flex items-center">
                          <FaMedal className="mr-2 text-yellow-400" />
                          Started:
                        </span>
                        <span className="text-gray-300">
                          {new Date(war.declaredAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <Link
                      to={`/wars/${war.id}`}
                      className="text-orange-400 hover:text-orange-300 text-sm mt-1 flex items-center"
                    >
                      <FaTrophy className="mr-1.5" />
                      View War Details
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-6">
                  You're not participating in any wars.
                </p>
                <Link
                  to="/wars/new"
                  className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white px-6 py-3 rounded-xl inline-block transition-all shadow hover:shadow-lg hover:shadow-red-500/20 flex items-center justify-center mx-auto"
                >
                  <FaFireAlt className="mr-2" />
                  Declare War
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Share Profile Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50 p-4">
          <div className="bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl max-w-md w-full p-6 border border-gray-700/50">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-white flex items-center">
                <FaShare className="mr-2 text-blue-400" />
                Share Your Profile
              </h2>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-4">
              <p className="text-gray-300 mb-3">
                Share your profile with others using this link:
              </p>
              <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-3 border border-gray-600/50">
                <div className="flex items-center justify-between">
                  <span className="text-blue-400 text-sm font-mono break-all mr-2">
                    {getShareableProfileUrl()}
                  </span>
                  <button
                    onClick={handleCopyProfileUrl}
                    className={`flex items-center px-3 py-2 rounded-lg text-sm transition-all ${
                      copySuccess
                        ? "bg-green-600 text-white"
                        : "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700"
                    }`}
                    title="Copy to clipboard"
                  >
                    {copySuccess ? (
                      <>
                        <FaCheck className="mr-1" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <FaCopy className="mr-1" />
                        Copy
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={() => setShowShareModal(false)}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create State Modal */}
      <CreateStateModal
        isOpen={isCreateStateModalOpen}
        onClose={() => setIsCreateStateModalOpen(false)}
        onSuccess={() => {
          refreshStateData();
          setIsCreateStateModalOpen(false);
        }}
      />

      {/* Create Party Modal */}
      <CreatePartyModal
        isOpen={isCreatePartyModalOpen}
        onClose={() => setIsCreatePartyModalOpen(false)}
        onSuccess={() => {
          refreshPartyData();
          setIsCreatePartyModalOpen(false);
        }}
      />

      {/* Balance Logs Modal */}
      <BalanceLogsModal
        isOpen={isBalanceLogsModalOpen}
        onClose={() => setIsBalanceLogsModalOpen(false)}
      />

      <Footer />
    </div>
  );
}
