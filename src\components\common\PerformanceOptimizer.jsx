import React, { Suspense, lazy } from 'react';

// Lazy load components for better performance
export const LazyImage = ({ src, alt, className, ...props }) => {
  const [imageSrc, setImageSrc] = React.useState(src);
  const [imageRef, setImageRef] = React.useState();

  React.useEffect(() => {
    let observer;
    let didCancel = false;

    if (imageRef && imageSrc === src) {
      if (IntersectionObserver) {
        observer = new IntersectionObserver(
          entries => {
            entries.forEach(entry => {
              if (
                !didCancel &&
                (entry.intersectionRatio > 0 || entry.isIntersecting)
              ) {
                setImageSrc(src);
                observer.unobserve(imageRef);
              }
            });
          },
          {
            threshold: 0.01,
            rootMargin: '75%',
          }
        );
        observer.observe(imageRef);
      } else {
        // Fallback for older browsers
        setImageSrc(src);
      }
    }
    return () => {
      didCancel = true;
      if (observer && observer.unobserve) {
        observer.unobserve(imageRef);
      }
    };
  }, [src, imageSrc, imageRef]);

  return (
    <img
      ref={setImageRef}
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
      {...props}
    />
  );
};

// SEO-friendly link component
export const SEOLink = ({ to, children, className, ...props }) => {
  const isExternal = to.startsWith('http');
  
  if (isExternal) {
    return (
      <a 
        href={to} 
        className={className}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    );
  }
  
  return (
    <a href={to} className={className} {...props}>
      {children}
    </a>
  );
};

// Loading component for Suspense
export const LoadingSpinner = () => (
  <div className="flex justify-center items-center p-4">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neonBlue"></div>
  </div>
);

// Error boundary for better UX
export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center p-8">
          <h2 className="text-xl font-bold text-white mb-4">Something went wrong</h2>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-neonBlue text-white rounded hover:bg-blue-600"
          >
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Preload critical resources
export const PreloadResources = () => {
  React.useEffect(() => {
    // Preload critical images
    const criticalImages = [
      '/wn-logo.png',
      '/wn-icon.png'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });

    // Preload critical CSS
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = '/src/index.css';
    document.head.appendChild(link);
  }, []);

  return null;
};

export default {
  LazyImage,
  SEOLink,
  LoadingSpinner,
  ErrorBoundary,
  PreloadResources
}; 