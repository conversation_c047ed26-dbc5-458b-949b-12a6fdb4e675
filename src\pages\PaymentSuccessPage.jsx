import React, { useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuthGuard } from '../hooks/useAuthGuard';
import Navbar from '../components/Navbar';
import { showSuccessToast } from '../utils/showSuccessToast';
import useUserDataStore from '../store/useUserDataStore';
import { FaCheckCircle, FaCrown, FaArrowRight, FaCoins } from 'react-icons/fa';

export default function PaymentSuccessPage() {
  useAuthGuard();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { fetchUserData } = useUserDataStore();

  // Get payment details from URL parameters
  const paymentType = searchParams.get('type') || 'subscription';
  const plan = searchParams.get('plan') || 'premium';
  const goldPackage = searchParams.get('package');
  const goldAmount = searchParams.get('amount');
  const giftedToUserId = searchParams.get('giftedToUserId');
  const giftedToUsername = searchParams.get('giftedToUsername');
  const durationDays = searchParams.get('durationDays');

  // Get plan display information
  const getPlanInfo = () => {
    switch(plan) {
      case 'monthly':
        return { name: 'Monthly Premium', price: '€2.00', period: 'per month' };
      case 'semiannual':
        return { name: '6-Month Premium', price: '€9.00', period: 'per 6 months' };
      case 'yearly':
        return { name: 'Yearly Premium', price: '€12.00', period: 'per year' };
      default:
        return { name: 'Premium', price: '', period: '' };
    }
  };

  // Get gold package display information
  const getGoldPackageInfo = () => {
    switch(goldPackage) {
      case 'small':
        return { name: 'Starter Pack', amount: 1000, bonus: 100, price: '€4.99' };
      case 'medium':
        return { name: 'Advanced Pack', amount: 3000, bonus: 500, price: '€9.99' };
      case 'large':
        return { name: 'Elite Pack', amount: 10000, bonus: 2000, price: '€24.99' };
      case 'extra_large':
        return { name: 'Ultimate Pack', amount: 25000, bonus: 5000, price: '€49.99' };
      default:
        return { name: 'Gold Package', amount: goldAmount ? parseInt(goldAmount) : 0, bonus: 0, price: '' };
    }
  };

  const planInfo = getPlanInfo();
  const goldInfo = getGoldPackageInfo();

  useEffect(() => {
    // Show success message
    showSuccessToast('Payment successful!');

    // Refresh user data to reflect new subscription status
    fetchUserData(true);

    // If no parameters are provided, redirect to home after 5 seconds
    if (!searchParams.get('type')) {
      const timer = setTimeout(() => {
        navigate('/home');
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [fetchUserData, navigate, searchParams]);

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="flex justify-center mb-6">
            <FaCheckCircle className="text-green-400 text-6xl" />
          </div>

          <h1 className="text-3xl font-bold text-white mb-4">Payment Successful!</h1>

          {paymentType === 'subscription' && (
            <>
              <p className="text-xl text-gray-300 mb-2">
                Thank you for subscribing to Warfront Nations Premium!
              </p>
              <p className="text-lg text-neonBlue mb-6">
                {planInfo.name} - {planInfo.price} {planInfo.period}
              </p>

              <div className="bg-gray-700 p-6 rounded-lg mb-8 max-w-md mx-auto">
                <div className="flex items-center justify-center mb-4">
                  <FaCrown className="text-yellow-400 text-2xl mr-2" />
                  <h2 className="text-xl font-semibold text-white">Premium Benefits Activated</h2>
                </div>
                <ul className="text-left space-y-3 mb-4">
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span className="text-gray-300">50% faster training times</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span className="text-gray-300">Auto-mode in wars</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span className="text-gray-300">Premium badge</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">✓</span>
                    <span className="text-gray-300">Priority customer support</span>
                  </li>
                </ul>
              </div>
            </>
          )}

          {paymentType === 'gift_premium' && (
            <>
              <p className="text-xl text-gray-300 mb-2">
                Premium gift sent successfully!
              </p>
              <p className="text-lg text-neonBlue mb-6">
                {planInfo.name} - {planInfo.price}
              </p>

              <div className="bg-gray-700 p-6 rounded-lg mb-8 max-w-md mx-auto">
                <div className="flex items-center justify-center mb-4">
                  <FaCrown className="text-yellow-400 text-2xl mr-2" />
                  <h2 className="text-xl font-semibold text-white">Gift Premium Delivered</h2>
                </div>
                <div className="text-center mb-4">
                  <p className="text-gray-300 mb-2">
                    Gifted to: <span className="text-white font-semibold">{giftedToUsername || 'Unknown User'}</span>
                  </p>
                  <p className="text-gray-300">
                    Duration: <span className="text-white font-semibold">{durationDays || '30'} days</span>
                  </p>
                </div>
                <p className="text-gray-300 text-center">
                  The premium benefits have been applied to the recipient's account immediately.
                </p>
              </div>
            </>
          )}

          {paymentType === 'gold' && (
            <>
              <p className="text-xl text-gray-300 mb-2">
                Your gold purchase has been processed successfully and added to your account!
              </p>
              <p className="text-lg text-neonBlue mb-6">
                {goldInfo.name} - {goldInfo.price}
              </p>

              <div className="bg-gray-700 p-6 rounded-lg mb-8 max-w-md mx-auto">
                <div className="flex items-center justify-center mb-4">
                  <FaCoins className="text-yellow-400 text-2xl mr-2" />
                  <h2 className="text-xl font-semibold text-white">Gold Added to Your Account</h2>
                </div>
                <div className="flex justify-center items-center space-x-2 mb-4">
                  <span className="text-yellow-400 text-3xl">🪙</span>
                  <span className="text-3xl font-bold text-white">{goldInfo.amount.toLocaleString()}</span>
                  {goldInfo.bonus > 0 && (
                    <span className="ml-2 bg-green-900 text-green-400 text-sm px-2 py-1 rounded">
                      +{goldInfo.bonus} BONUS
                    </span>
                  )}
                </div>
                <p className="text-gray-300 text-center">
                  You can use gold to accelerate training, purchase special items, and gain advantages in various game aspects.
                </p>
              </div>
            </>
          )}

          <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
            <Link
              to="/home"
              className="bg-neonBlue hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center"
            >
              Return to Dashboard
            </Link>

            <Link
              to="/profile"
              className="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center"
            >
              View Profile <FaArrowRight className="ml-2" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
