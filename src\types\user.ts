import { Party } from './party';
import { Region } from './region';
import { Factory } from './factory';
import { WorkSession } from './factory';

export interface User {
  id: number;
  username: string;
  email: string;
  avatarUrl?: string;
  level: number;
  experience: number;
  energy: number;
  strength: number;
  endurance: number;
  intelligence: number;
  gold: number;
  money: number;
  trainingExpiresAt?: Date;
  aboutMe?: string;
  isPremium: boolean;
  premiumExpiresAt?: Date;
  isActive: boolean;
  leadingParty?: Party;
  memberOfParty?: Party;
  region: Region;
  ownedFactories?: Factory[];
  workSessions?: WorkSession[];
  createdAt: Date;
  updatedAt: Date;
}



export interface UserProfile {
  id: number;
  username: string;
  avatarUrl?: string | null;
  level: number;
  experience: number;
  strength: number;
  endurance: number;
  intelligence: number;
  trainingExpiresAt?: Date;
  aboutMe?: string;
  isPremium: boolean;
  premiumExpiresAt?: Date;
  isActive: boolean;
  region: {
    id: string;
    name: string;
    population?: number;
    resources?: Record<string, number>;
    state?: {
      id: string;
      name: string;
      leader?: { id: number; username: string };
      regionsCount?: number;
    };
  };
  memberOfParty?: {
    id: string;
    name: string;
    leader?: { id: number; username: string };
    membersCount?: number;
    region?: { id: string; name: string; population?: number };
  };
  leadingParty?: {
    id: string;
    name: string;
    leader?: { id: number; username: string };
    membersCount?: number;
    region?: { id: string; name: string; population?: number };
  };
  createdAt: Date;
  updatedAt: Date;
} 