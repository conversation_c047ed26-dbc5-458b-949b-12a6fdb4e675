import { api } from './api';
import { PaginatedMessagesResponse, Message, SendMessageDto } from '../../types/chat';

export interface GeneralChatInfo {
  chat: {
    id: string;
    type: 'general';
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  };
  unreadCount: number;
  lastMessage?: Message;
}

export class GeneralChatService {
  /**
   * Get general chat information
   */
  static async getGeneralChatInfo(): Promise<GeneralChatInfo> {
    const response = await api.get('/chats/general/info');
    return response.data;
  }

  /**
   * Get general chat messages with pagination
   */
  static async getGeneralChatMessages(
    limit: number = 50,
    cursor?: string,
  ): Promise<PaginatedMessagesResponse> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (cursor) params.append('cursor', cursor);

    const response = await api.get(`/chats/general/messages?${params.toString()}`);
    return response.data;
  }

  /**
   * Send a message to general chat
   */
  static async sendGeneralChatMessage(messageData: SendMessageDto): Promise<Message> {
    const response = await api.post('/chats/general/messages', messageData);
    return response.data;
  }

  /**
   * Mark general chat messages as read
   */
  static async markGeneralChatAsRead(): Promise<{ markedCount: number }> {
    const response = await api.put('/chats/general/read');
    return response.data;
  }

  /**
   * Get unread message count for general chat
   */
  static async getGeneralChatUnreadCount(): Promise<{ unreadCount: number }> {
    const response = await api.get('/chats/general/unread-count');
    return response.data;
  }

  /**
   * Get unread message count for general chat (separate endpoint)
   */
  static async getGeneralChatUnreadCountByUser(): Promise<{ unreadCount: number }> {
    const response = await api.get('/chats/general-chat-unread-count');
    return response.data;
  }
} 