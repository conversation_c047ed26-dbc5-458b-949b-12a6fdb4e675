import React, { useState, useEffect } from 'react';
import Joyride, { ACTIONS, EVENTS, STATUS } from 'react-joyride-react19-compat';
import useUserDataStore from '../../store/useUserDataStore';
import { shouldShowIntro, markIntroCompleted, resetIntroStatus } from '../../utils/introUtils';
import './NewUserIntro.css';

const NewUserIntro = ({ forceStart = false }) => {
  const { userData } = useUserDataStore();
  const [runTour, setRunTour] = useState(false);
  const [stepIndex, setStepIndex] = useState(0);

  useEffect(() => {
    // Start tour if conditions are met
    if (userData && shouldShowIntro(userData, forceStart)) {
      // Small delay to ensure DOM elements are rendered
      setTimeout(() => {
        setRunTour(true);
      }, 1000);
    }
  }, [userData, forceStart]);

  const steps = [
    {
      target: 'body',
      content: (
        <div className="warfront-intro-welcome">
          <h2 className="text-2xl font-bold text-white mb-4">🎖️ Welcome to Warfront Nations!</h2>
          <p className="text-gray-300 mb-4">
            Prepare for battle, Commander! You're about to enter a world of strategic warfare,
            political intrigue, and economic domination.
          </p>
          <p className="text-neonBlue font-semibold">Let's get you started with a quick tour!</p>
        </div>
      ),
      placement: 'center',
      disableBeacon: true,
    },
    {
      target: '[data-tour="stats-grid"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">📊 Global Statistics</h3>
          <p className="text-gray-300 mb-3">
            These cards show the current state of the world - total states, regions, population, and active wars.
          </p>
          <p className="text-neonBlue">Click on any card to explore in detail!</p>
        </div>
      ),
      placement: 'bottom',
    },
    {
      target: '[data-tour="my-state"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">🏛️ Your State</h3>
          <p className="text-gray-300 mb-3">
            This is your political home. You can create your own state or join an existing one.
          </p>
          <p className="text-yellow-400 mb-2">💡 Pro Tip:</p>
          <p className="text-gray-300">States provide protection, resources, and political power!</p>
        </div>
      ),
      placement: 'left',
    },
    {
      target: '[data-tour="active-wars"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">⚔️ Active Wars</h3>
          <p className="text-gray-300 mb-3">
            Here you'll see ongoing conflicts. Wars are the heart of Warfront Nations!
          </p>
          <p className="text-red-400 mb-2">🔥 Remember:</p>
          <p className="text-gray-300">Participate in wars to gain experience, resources, and glory!</p>
        </div>
      ),
      placement: 'left',
    },
    {
      target: '[data-tour="travel-section"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">✈️ Travel System</h3>
          <p className="text-gray-300 mb-3">
            Move between regions to explore new opportunities, join different battles, or establish your presence.
          </p>
          <p className="text-blue-400">Travel costs money but opens up new possibilities!</p>
        </div>
      ),
      placement: 'bottom',
    },
    {
      target: '[data-tour="global-stats"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">📈 War Analytics</h3>
          <p className="text-gray-300 mb-3">
            Track global war statistics and see how conflicts are shaping the world.
          </p>
          <p className="text-purple-400">Knowledge is power in warfare!</p>
        </div>
      ),
      placement: 'top',
    },
    {
      target: '[data-tour="chat-widget"]',
      content: (
        <div>
          <h3 className="text-xl font-bold text-white mb-3">💬 Global Chat</h3>
          <p className="text-gray-300 mb-3">
            Connect with other players, form alliances, or engage in diplomatic discussions.
          </p>
          <p className="text-green-400">Communication is key to victory!</p>
        </div>
      ),
      placement: 'left',
    },
    {
      target: 'body',
      content: (
        <div className="warfront-intro-completion">
          <h2 className="text-2xl font-bold text-white mb-4">🚀 Ready for Battle!</h2>
          <p className="text-gray-300 mb-4">
            You're now ready to begin your journey in Warfront Nations!
          </p>
          <div className="warfront-intro-tips-box">
            <h4 className="text-lg font-semibold text-neonBlue mb-2">Quick Start Tips:</h4>
            <ul className="text-left text-gray-300 space-y-1">
              <li>Create or join a state for protection</li>
              <li>Train your skills to become stronger</li>
              <li>Participate in wars to gain experience</li>
              <li>Build factories for economic power</li>
              <li>Form alliances with other players</li>
            </ul>
          </div>
          <p className="text-yellow-400 font-semibold">Good luck, Commander! 🎖️</p>
        </div>
      ),
      placement: 'center',
    },
  ];

  const handleJoyrideCallback = (data) => {
    const { action, index, status, type } = data;

    if ([EVENTS.STEP_AFTER, EVENTS.TARGET_NOT_FOUND].includes(type)) {
      setStepIndex(index + (action === ACTIONS.PREV ? -1 : 1));
    } else if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status)) {
      // Mark intro as completed
      markIntroCompleted();
      setRunTour(false);
      setStepIndex(0);
    }
  };

  // Add a global function for testing (only in development)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      window.resetWarfrontIntro = () => {
        resetIntroStatus();
        setRunTour(true);
        setStepIndex(0);
      };
    }
  }, []);

  // Custom styles to match the game's theme
  const joyrideStyles = {
    options: {
      primaryColor: '#646cff', // neonBlue
      backgroundColor: '#1f2937', // gray-800
      textColor: '#ffffff',
      overlayColor: 'rgba(0, 0, 0, 0.85)',
      arrowColor: '#1f2937',
      zIndex: 10000,
    },
    tooltip: {
      backgroundColor: '#1f2937',
      borderRadius: '16px',
      border: '2px solid #646cff',
      boxShadow: '0 25px 50px -12px rgba(100, 108, 255, 0.25), 0 0 0 1px rgba(100, 108, 255, 0.1)',
      maxWidth: '400px',
      className: 'warfront-intro-tooltip',
    },
    tooltipContainer: {
      textAlign: 'left',
      padding: '20px',
    },
    tooltipTitle: {
      color: '#ffffff',
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '8px',
    },
    tooltipContent: {
      color: '#d1d5db',
      fontSize: '14px',
      lineHeight: '1.6',
    },
    buttonNext: {
      backgroundColor: '#646cff',
      borderRadius: '8px',
      border: 'none',
      color: '#ffffff',
      fontSize: '14px',
      fontWeight: '600',
      padding: '10px 20px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: '0 4px 12px rgba(100, 108, 255, 0.3)',
      className: 'warfront-intro-button-next',
    },
    buttonBack: {
      backgroundColor: '#374151',
      borderRadius: '8px',
      border: 'none',
      color: '#ffffff',
      fontSize: '14px',
      fontWeight: '600',
      padding: '10px 20px',
      cursor: 'pointer',
      marginRight: '12px',
      transition: 'all 0.2s ease',
      className: 'warfront-intro-button-back',
    },
    buttonSkip: {
      backgroundColor: 'transparent',
      border: '1px solid #6b7280',
      borderRadius: '8px',
      color: '#9ca3af',
      fontSize: '14px',
      fontWeight: '600',
      padding: '10px 20px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      className: 'warfront-intro-button-skip',
    },
    beacon: {
      backgroundColor: '#646cff',
      border: '3px solid #ffffff',
      boxShadow: '0 0 20px rgba(100, 108, 255, 0.6)',
      className: 'warfront-intro-beacon',
    },
    beaconInner: {
      backgroundColor: '#646cff',
    },
    spotlight: {
      backgroundColor: 'transparent',
      border: '3px solid #646cff',
      borderRadius: '12px',
      boxShadow: '0 0 30px rgba(100, 108, 255, 0.4)',
      className: 'warfront-intro-spotlight',
    },
    overlay: {
      className: 'warfront-intro-overlay',
    },
  };

  if (!runTour) return null;

  return (
    <Joyride
      steps={steps}
      run={runTour}
      stepIndex={stepIndex}
      callback={handleJoyrideCallback}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      styles={joyrideStyles}
      locale={{
        back: 'Previous',
        close: 'Close',
        last: 'Finish Tour',
        next: 'Next',
        skip: 'Skip Tour',
      }}
      floaterProps={{
        disableAnimation: false,
      }}
    />
  );
};

export default NewUserIntro;
