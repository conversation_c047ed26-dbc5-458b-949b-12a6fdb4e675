import React from "react";
import { useLocation, Link } from "react-router-dom";
import { Home, Briefcase, Sword, User } from "lucide-react";

const BottomTabs = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  const tabs = [
    {
      path: "/home",
      icon: Home,
      label: "Home",
      color: "text-neonBlue",
    },
    {
      path: "/jobs",
      icon: Briefcase,
      label: "Jobs",
      color: "text-yellow-400",
    },
    {
      path: "/wars",
      icon: Sword,
      label: "Wars",
      color: "text-red-500",
    },
    {
      path: "/profile",
      icon: User,
      label: "Profile",
      color: "text-purple-400",
    },
  ];

  const isActive = (path) => currentPath.startsWith(path);

  return (
    <>
      <div className="md:hidden h-16" /> {/* Spacer */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 z-50">
        <div className="grid grid-cols-4">
          {tabs.map((tab) => (
            <Link
              key={tab.path}
              to={tab.path}
              className="flex flex-col items-center py-3"
            >
              <tab.icon
                className={isActive(tab.path) ? tab.color : "text-gray-400"}
                size={20}
              />
              <span
                className={`text-xs mt-1 ${
                  isActive(tab.path) ? tab.color : "text-gray-400"
                }`}
              >
                {tab.label}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
};

export default BottomTabs;
