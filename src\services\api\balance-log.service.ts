import { api } from './api';

export interface BalanceLog {
  id: string;
  userId: number;
  balanceType: 'gold' | 'money';
  transactionType: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  description?: string;
  metadata?: Record<string, any>;
  relatedTransactionId?: string;
  relatedUserId?: number;
  createdAt: string;
}

export interface BalanceLogsPaginatedResponse {
  data: BalanceLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BalanceSummary {
  userId: number;
  currentGold: number;
  currentMoney: number;
  totalGoldEarned: number;
  totalGoldSpent: number;
  totalMoneyEarned: number;
  totalMoneySpent: number;
  lastTransactionDate?: string;
}

export interface GetBalanceLogsParams {
  page?: number;
  limit?: number;
  balanceType?: 'gold' | 'money';
  transactionType?: string;
  startDate?: string;
  endDate?: string;
}

export const balanceLogService = {
  // Get paginated balance logs
  getBalanceLogs: async (params: GetBalanceLogsParams = {}): Promise<BalanceLogsPaginatedResponse> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.balanceType) searchParams.append('balanceType', params.balanceType);
    if (params.transactionType) searchParams.append('transactionType', params.transactionType);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);

    const response = await api.get(`/balance-logs?${searchParams.toString()}`);
    return response.data;
  },

  // Get balance summary
  getBalanceSummary: async (): Promise<BalanceSummary> => {
    const response = await api.get('/balance-logs/summary');
    return response.data;
  },

  // Get recent balance logs
  getRecentBalanceLogs: async (limit: number = 10): Promise<BalanceLog[]> => {
    const response = await api.get(`/balance-logs/recent?limit=${limit}`);
    return response.data;
  },

  // Get balance logs by transaction type
  getBalanceLogsByType: async (transactionType: string, limit: number = 50): Promise<BalanceLog[]> => {
    const response = await api.get(`/balance-logs/type/${transactionType}?limit=${limit}`);
    return response.data;
  },

  // Get balance logs for date range
  getBalanceLogsByDateRange: async (startDate: string, endDate: string): Promise<BalanceLog[]> => {
    const response = await api.get(`/balance-logs/date-range?startDate=${startDate}&endDate=${endDate}`);
    return response.data;
  },

  // Get balance change in range
  getBalanceChangeInRange: async (
    balanceType: 'gold' | 'money',
    startDate: string,
    endDate: string
  ): Promise<{ totalChange: number; transactionCount: number }> => {
    const response = await api.get(
      `/balance-logs/change-in-range?balanceType=${balanceType}&startDate=${startDate}&endDate=${endDate}`
    );
    return response.data;
  },

  // Get balance logs with related users
  getBalanceLogsWithRelatedUsers: async (params: GetBalanceLogsParams = {}): Promise<BalanceLogsPaginatedResponse> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.append('page', params.page.toString());
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.balanceType) searchParams.append('balanceType', params.balanceType);
    if (params.transactionType) searchParams.append('transactionType', params.transactionType);
    if (params.startDate) searchParams.append('startDate', params.startDate);
    if (params.endDate) searchParams.append('endDate', params.endDate);

    const response = await api.get(`/balance-logs/with-users?${searchParams.toString()}`);
    return response.data;
  },
}; 