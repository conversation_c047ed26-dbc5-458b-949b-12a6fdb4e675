import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import api from "../services/api/api";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import { FaEye, FaEyeSlash, FaChevronDown } from "react-icons/fa";
import { useRedirectIfAuthenticated } from "../hooks/useRedirectIfAuthenticated";

export default function Register() {
  // Redirect to home if user is already authenticated
  useRedirectIfAuthenticated();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [username, setUsername] = useState("");
  const [loading, setLoading] = useState(false);
  const [regionId, setRegionId] = useState("");
  const [regions, setRegions] = useState([]);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { status } = await api.post("/auth/register", {
        username,
        email,
        password,
        regionId,
      });

      if (status === 201) {
        window.gtag("event", "sign_up", {
          method: "email",
        });
        
        showSuccessToast(
          "Account created! Please check your email to verify your account"
        );
        navigate("/login");
      }
    } catch (error) {
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchRegions = async () => {
      try {
        const { data } = await api.get("/regions");
        setRegions(data);
      } catch (error) {
        console.error("Failed to load regions");
      }
    };

    fetchRegions();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0F0F10] via-[#1C1C1E] to-[#1E2A4A] flex items-center justify-center px-4">
      <div className="w-full max-w-md bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 shadow-xl font-military">
        <div className="text-center mb-6">
          <p className="text-sm text-center text-[#A1A1A1] space-y-1">
            <a href="/login" className="hover:text-white">
              Back to login page
            </a>
          </p>
          <br></br>
          <Link to="/" className="flex items-center">
            <img
              src="/wn-logo.png"
              alt="Warfront Nations Logo"
              className="h-14 mx-auto mb-4"
            />
          </Link>
          <h2 className="text-2xl font-bold text-white">Register</h2>
          <p className="mt-1 text-sm text-[#A1A1A1]">
            Now is the time to begin your legacy.
          </p>
        </div>
        <form onSubmit={handleRegister}>
          <input
            type="text"
            className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
            placeholder="Username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
          />

          <input
            type="email"
            className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />

          <div className="relative">
            <input
              type={showPassword ? "text" : "password"}
              className="w-full mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <span
              className="absolute right-4 top-4 cursor-pointer text-gray-400"
              onClick={() => setShowPassword((prev) => !prev)}
            >
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          </div>

          <div className="relative mb-3">
            <select
              className="w-full appearance-none mb-4 px-4 py-3 rounded-xl bg-[#1C1C1E] text-white placeholder-[#777] focus:ring-2 focus:ring-[#00D1FF] focus:outline-none border border-white/10"
              value={regionId}
              onChange={(e) => setRegionId(e.target.value)}
              required
            >
              <option value="">Select your region</option>
              {regions.map((region) => (
                <option key={region.id} value={region.id}>
                  {region.name}
                </option>
              ))}
            </select>
            <div class="pointer-events-none absolute right-4 top-4 items-center">
              <svg
                class="w-4 h-4 text-gray-400"
                fill="none"
                stroke="currentColor"
              >
                <FaChevronDown />
              </svg>
            </div>
          </div>
          <button
            type="submit"
            className="mt-3 w-full bg-[#1C1C1E] border border-white/20 text-white font-bold py-3 rounded-xl transition-all hover:bg-[#2D2D2E]"
            disabled={loading}
          >
            {loading ? "Creating account..." : "Register"}
          </button>
        </form>
      </div>
    </div>
  );
}
