import { api } from "./api";

export enum OAuthProvider {
  GOOGLE = "google",
}

export interface OAuthLoginRequest {
  provider: OAuthProvider;
  accessToken: string;
  ipAddress?: string;
}

export interface OAuthLoginResponse {
  access_token: string;
  chats: number;
  user: any;
}

export class OAuthService {
  static async login(request: OAuthLoginRequest): Promise<OAuthLoginResponse> {
    const response = await api.post("/auth/oauth/login", request);
    return response.data;
  }

  static async loginWithGoogle(
    accessToken: string,
    ipAddress?: string
  ): Promise<OAuthLoginResponse> {
    const result = await this.login({
      provider: OAuthProvider.GOOGLE,
      accessToken,
      ipAddress,
    });
    if (result?.user?.experience === 0) {
      window.gtag?.("event", "sign_up", {
        method: "google",
      });
    }

    return result;
  }
}
