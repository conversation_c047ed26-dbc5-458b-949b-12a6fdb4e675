import { create } from 'zustand';
import { chatService } from '../services/api/chat.service.js';
import { GeneralChatService } from '../services/api/general-chat.service.js';
import { chatWebSocketService } from '../services/chat/chatWebSocket.service.js';
import { CHAT_CONFIG } from '../config/chat.config.js';
import useAuthStore from './useAuthStore.js';
import DOMPurify from 'dompurify';

/**
 * Zustand store for chat state management
 * Handles chat data, WebSocket connection, and UI state
 */
const useChatStore = create((set, get) => ({
  // Connection state
  isConnected: false,
  isConnecting: false,
  connectionError: null,

  // Chats data
  chats: [],
  chatsLoading: false,
  chatsError: null,
  chatsHasMore: true,
  chatsCursor: null,

  // Messages data
  messages: {}, // { chatId: Message[] }
  messagesLoading: {}, // { chatId: boolean }
  messagesError: {}, // { chatId: string | null }
  messagesHasMore: {}, // { chatId: boolean }
  messagesCursor: {}, // { chatId: string | undefined }

  // UI state
  activeChatId: null,
  typingUsers: {}, // { chatId: TypingEvent[] }

  // Unread counts
  totalUnreadCount: 0, // Personal messages only
  generalChatUnreadCount: 0, // General chat messages only

  // Actions
  /**
   * Initialize WebSocket connection
   */
  connect: async () => {
    const state = get();

    // Prevent multiple connection attempts
    if (state.isConnected || state.isConnecting) {
      console.log('ChatStore: Connection already exists or in progress');
      return Promise.resolve();
    }

    const { access_token } = useAuthStore.getState();
    if (!access_token) {
      console.error('ChatStore: No access token available');
      set({ connectionError: 'No authentication token available' });
      return Promise.reject(new Error('No authentication token available'));
    }

    set({ isConnecting: true, connectionError: null });

    // Setup event handlers only once
    if (!chatWebSocketService.eventHandlers.has('connection_status_changed')) {
      chatWebSocketService.on('connection_status_changed', (data) => {
        console.log('ChatStore: Connection status changed:', data);
        set({
          isConnected: data.isConnected,
          isConnecting: data.status === 'connecting',
          connectionError: data.error || null,
        });

        // If connection fails, show a helpful message but don't block the UI
        if (data.error && !data.isConnected) {
          console.warn('ChatStore: WebSocket connection failed, using HTTP-only mode');
          // Don't set isConnecting to false immediately, let the timeout handle it
          setTimeout(() => {
            set({ isConnecting: false });
          }, 2000);
        }
      });

      chatWebSocketService.on('new_message', get().handleNewMessage);
      chatWebSocketService.on('messages_read', get().handleMessagesRead);
      chatWebSocketService.on('user_typing', get().handleTyping);
    }

    // Connect to WebSocket with Promise handling
    try {
      await chatWebSocketService.connect(access_token);
      console.log('ChatStore: WebSocket connection successful');
      return Promise.resolve();
    } catch (error) {
      console.error('ChatStore: Failed to connect to WebSocket:', error);
      set({
        isConnecting: false,
        connectionError: error.message || 'Failed to connect to chat server'
      });
      return Promise.reject(error);
    }
  },

  /**
   * Disconnect WebSocket
   */
  disconnect: () => {
    chatWebSocketService.disconnect();
    set({
      isConnected: false,
      isConnecting: false,
      connectionError: null,
    });
  },

  /**
   * Fetch user's chats
   */
  fetchChats: async (cursor = null) => {
    const state = get();

    // Don't fetch if already loading
    if (state.chatsLoading) return;

    set({ chatsLoading: true, chatsError: null });

    try {
      const response = await chatService.getChats(CHAT_CONFIG.CHATS.PAGE_SIZE, cursor);

      set((state) => ({
        chats: cursor ? [...state.chats, ...response.chats] : response.chats,
        chatsHasMore: response.hasMore,
        chatsCursor: response.nextCursor,
        chatsLoading: false,
        totalUnreadCount: response.chats.reduce((total, chat) => total + chat.unreadCount, 0),
      }));
    } catch (error) {
      console.error('ChatStore: Failed to fetch chats:', error);
      set({
        chatsError: error.message,
        chatsLoading: false,
      });
    }
  },

  /**
   * Create a new chat
   */
  createChat: async (chatData) => {
    try {
      const newChat = await chatService.createChat(chatData);

      set((state) => ({
        chats: [newChat, ...state.chats],
      }));

      return newChat;
    } catch (error) {
      console.error('ChatStore: Failed to create chat:', error);
      throw error;
    }
  },

  /**
   * Find existing direct chat with a specific user
   */
  findDirectChatWithUser: (userId) => {
    const { chats } = get();
    const { user } = useAuthStore.getState();

    return chats.find(chat =>
      chat.type === 'direct' &&
      chat.participants.length === 2 &&
      chat.participants.some(participant => participant.id === userId) &&
      chat.participants.some(participant => participant.id === user?.id)
    );
  },

  /**
   * Create or find direct chat with a user
   * This method ensures no duplicate direct chats are created
   */
  createOrFindDirectChat: async (userId) => {
    try {
      // First, check if a direct chat already exists
      let existingChat = get().findDirectChatWithUser(userId);

      if (existingChat) {
        return existingChat;
      }

      // If not found, refresh chats and check again
      await get().fetchChats();
      existingChat = get().findDirectChatWithUser(userId);

      if (existingChat) {
        return existingChat;
      }

      // If still not found, create new direct chat
      const newChat = await get().createChat({
        type: 'direct',
        participantIds: [userId],
      });

      return newChat;
    } catch (error) {
      console.error('ChatStore: Failed to create or find direct chat:', error);
      throw error;
    }
  },

  /**
   * Set active chat
   */
  setActiveChat: (chatId) => {
    set({ activeChatId: chatId });

    // Mark messages as read when opening a chat
    if (chatId) {
      get().markAsRead(chatId);
    }
  },

  /**
   * Fetch messages for a chat
   */
  fetchMessages: async (chatId, cursor = null) => {
    const state = get();

    // Don't fetch if already loading
    if (state.messagesLoading[chatId]) return;

    set((state) => ({
      messagesLoading: {
        ...state.messagesLoading,
        [chatId]: true,
      },
      messagesError: {
        ...state.messagesError,
        [chatId]: null,
      },
    }));

    try {
      const response = await chatService.getMessages(chatId, CHAT_CONFIG.MESSAGES.PAGE_SIZE, cursor);

      set((state) => ({
        messages: {
          ...state.messages,
          [chatId]: cursor
            ? [...response.messages, ...(state.messages[chatId] || [])]
            : response.messages,
        },
        messagesHasMore: {
          ...state.messagesHasMore,
          [chatId]: response.hasMore,
        },
        messagesCursor: {
          ...state.messagesCursor,
          [chatId]: response.nextCursor,
        },
        messagesLoading: {
          ...state.messagesLoading,
          [chatId]: false,
        },
      }));
    } catch (error) {
      console.error('ChatStore: Failed to fetch messages:', error);
      set((state) => ({
        messagesError: {
          ...state.messagesError,
          [chatId]: error.message,
        },
        messagesLoading: {
          ...state.messagesLoading,
          [chatId]: false,
        },
      }));
    }
  },

  /**
   * Fetch general chat messages
   */
  fetchGeneralChatMessages: async (cursor = null) => {
    const state = get();

    // Don't fetch if already loading
    if (state.messagesLoading['general']) return;

    set((state) => ({
      messagesLoading: {
        ...state.messagesLoading,
        ['general']: true,
      },
      messagesError: {
        ...state.messagesError,
        ['general']: null,
      },
    }));

    try {
      const response = await GeneralChatService.getGeneralChatMessages(CHAT_CONFIG.MESSAGES.PAGE_SIZE, cursor);

      set((state) => ({
        messages: {
          ...state.messages,
          ['general']: cursor
            ? [...response.messages, ...(state.messages['general'] || [])]
            : response.messages,
        },
        messagesHasMore: {
          ...state.messagesHasMore,
          ['general']: response.hasMore,
        },
        messagesCursor: {
          ...state.messagesCursor,
          ['general']: response.nextCursor,
        },
        messagesLoading: {
          ...state.messagesLoading,
          ['general']: false,
        },
      }));
    } catch (error) {
      console.error('ChatStore: Failed to fetch general chat messages:', error);
      set((state) => ({
        messagesError: {
          ...state.messagesError,
          ['general']: error.message,
        },
        messagesLoading: {
          ...state.messagesLoading,
          ['general']: false,
        },
      }));
    }
  },

  /**
   * Send a message
   */
  sendMessage: async (chatId, content) => {
    try {
      // Sanitize content
      const sanitizedContent = CHAT_CONFIG.SECURITY.SANITIZE_HTML
        ? DOMPurify.sanitize(content, { ALLOWED_TAGS: [] })
        : content;

      // Try WebSocket first, fallback to HTTP
      if (get().isConnected) {
        chatWebSocketService.sendMessage(chatId, {
          content: sanitizedContent,
          type: 'text',
        });
      } else {
        // HTTP fallback
        const message = await chatService.sendMessage(chatId, sanitizedContent);

        // Add message to local state (check for duplicates)
        set((state) => {
          const existingMessages = state.messages[chatId] || [];
          const messageExists = existingMessages.some(existingMsg => existingMsg.id === message.id);

          if (messageExists) {
            console.log('ChatStore: Duplicate message from HTTP fallback, skipping:', message.id);
            return state;
          }

          return {
            messages: {
              ...state.messages,
              [chatId]: [...existingMessages, message],
            },
          };
        });
      }
    } catch (error) {
      console.error('ChatStore: Failed to send message:', error);
      throw error;
    }
  },

  /**
   * Mark messages as read
   */
  markAsRead: async (chatId) => {
    try {
      // Try WebSocket first, fallback to HTTP
      if (get().isConnected) {
        chatWebSocketService.markAsRead(chatId);
      } else {
        await chatService.markAsRead(chatId);
      }

      // Update local unread count
      set((state) => ({
        chats: state.chats.map(chat =>
          chat.id === chatId
            ? { ...chat, unreadCount: 0 }
            : chat
        ),
        totalUnreadCount: Math.max(0, state.totalUnreadCount - (state.chats.find(c => c.id === chatId)?.unreadCount || 0)),
      }));
    } catch (error) {
      console.error('ChatStore: Failed to mark messages as read:', error);
    }
  },

  /**
   * Start typing indicator
   */
  startTyping: (chatId) => {
    if (get().isConnected) {
      chatWebSocketService.startTyping(chatId);
    }
  },

  /**
   * Stop typing indicator
   */
  stopTyping: (chatId) => {
    if (get().isConnected) {
      chatWebSocketService.stopTyping(chatId);
    }
  },

  /**
   * Handle new message from WebSocket
   */
  handleNewMessage: (data) => {
    const { chatId, message } = data;
    const { user } = useAuthStore.getState();

    set((state) => {
      // Check if message already exists to prevent duplicates
      const existingMessages = state.messages[chatId] || [];
      const messageExists = existingMessages.some(existingMsg => existingMsg.id === message.id);

      if (messageExists) {
        console.log('ChatStore: Duplicate message detected, skipping:', message.id);
        return state; // Don't update state if message already exists
      }

      // For general chat, replace optimistic messages with real message
      let updatedMessages;
      if (chatId === 'general' || message.chat?.type === 'general') {
        const filteredMessages = existingMessages.filter(msg => !msg.isOptimistic);
        updatedMessages = {
          ...state.messages,
          ['general']: [...filteredMessages, message],
        };
      } else {
        updatedMessages = {
          ...state.messages,
          [chatId]: [...existingMessages, message],
        };
      }

      // Update chat's last message and unread count
      const updatedChats = state.chats.map(chat => {
        if (chat.id === chatId) {
          const isOwnMessage = message.sender.id === user?.id;
          return {
            ...chat,
            lastMessage: message,
            unreadCount: isOwnMessage ? chat.unreadCount : chat.unreadCount + 1,
            updatedAt: message.createdAt,
          };
        }
        return chat;
      });

      // Check if this is a general chat message
      const isGeneralChat = message.chat?.type === 'general' || chatId === 'general';
      
      // Update appropriate unread count
      let updatedState = {
        messages: updatedMessages,
        chats: updatedChats,
      };

      if (isGeneralChat) {
        // Update general chat unread count
        const newGeneralUnreadCount = message.sender.id !== user?.id ? 1 : 0;
        updatedState.generalChatUnreadCount = state.generalChatUnreadCount + newGeneralUnreadCount;
      } else {
        // Update personal chat unread count
        const newUnreadCount = message.sender.id !== user?.id ? 1 : 0;
        updatedState.totalUnreadCount = state.totalUnreadCount + newUnreadCount;
      }

      return updatedState;
    });

    // Show notification for new messages (if not from current user)
    if (message.sender.id !== useAuthStore.getState().user?.id) {
      get().showNotification(message, chatId);
    }
  },

  /**
   * Handle messages read event from WebSocket
   */
  handleMessagesRead: (data) => {
    const { chatId, userId } = data;
    const { user } = useAuthStore.getState();

    // Only update if it's our own read status
    if (userId === user?.id) {
      set((state) => ({
        chats: state.chats.map(chat =>
          chat.id === chatId
            ? { ...chat, unreadCount: 0 }
            : chat
        ),
      }));
    }
  },

  /**
   * Handle typing event from WebSocket
   */
  handleTyping: (data) => {
    const { chatId, userId, username, isTyping } = data;
    const { user } = useAuthStore.getState();

    // Don't show typing indicator for current user
    if (userId === user?.id) return;

    set((state) => {
      const currentTyping = state.typingUsers[chatId] || [];

      if (isTyping) {
        // Add or update typing user
        const existingIndex = currentTyping.findIndex(t => t.userId === userId);
        const typingEvent = { chatId, userId, username, isTyping };

        const updatedTyping = existingIndex >= 0
          ? currentTyping.map((t, i) => i === existingIndex ? typingEvent : t)
          : [...currentTyping, typingEvent];

        return {
          typingUsers: {
            ...state.typingUsers,
            [chatId]: updatedTyping,
          },
        };
      } else {
        // Remove typing user
        return {
          typingUsers: {
            ...state.typingUsers,
            [chatId]: currentTyping.filter(t => t.userId !== userId),
          },
        };
      }
    });

    // Auto-remove typing indicator after timeout
    if (isTyping) {
      setTimeout(() => {
        set((state) => ({
          typingUsers: {
            ...state.typingUsers,
            [chatId]: (state.typingUsers[chatId] || []).filter(t => t.userId !== userId),
          },
        }));
      }, CHAT_CONFIG.MESSAGES.TYPING_TIMEOUT);
    }
  },

  /**
   * Show notification for new message
   */
  showNotification: (message, chatId) => {
    if (!CHAT_CONFIG.NOTIFICATIONS.SHOW_DESKTOP_NOTIFICATIONS) return;

    // Check if notifications are supported and permitted
    if ('Notification' in window && Notification.permission === 'granted') {
      const chat = get().chats.find(c => c.id === chatId);
      const title = chat?.type === 'direct'
        ? `${message.sender.username}`
        : `${message.sender.username} in ${chat?.name || 'Group Chat'}`;

      new Notification(title, {
        body: message.content,
        icon: '/wn-icon.png',
        tag: `chat-${chatId}`,
      });
    }
  },

  /**
   * Request notification permission
   */
  requestNotificationPermission: async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return Notification.permission === 'granted';
  },

  /**
   * Get chat by ID
   */
  getChatById: (chatId) => {
    return get().chats.find(chat => chat.id === chatId);
  },

  /**
   * Get total unread count
   */
  getUnreadCount: () => {
    return get().totalUnreadCount;
  },

  setTotalUnreadCount: (count) => set({ totalUnreadCount: count }),
  setGeneralChatUnreadCount: (count) => set({ generalChatUnreadCount: count }),

  /**
   * Send message to general chat
   */
  sendGeneralChatMessage: async (content) => {
    try {
      // Sanitize content
      const sanitizedContent = CHAT_CONFIG.SECURITY.SANITIZE_HTML
        ? DOMPurify.sanitize(content, { ALLOWED_TAGS: [] })
        : content;

      // Create optimistic message
      const optimisticMessage = {
        id: `temp-${Date.now()}`,
        content: sanitizedContent,
        type: 'text',
        sender: useAuthStore.getState().user,
        createdAt: new Date().toISOString(),
        isOptimistic: true,
      };

      // Add optimistic message immediately
      set((state) => {
        const existingMessages = state.messages['general'] || [];
        return {
          messages: {
            ...state.messages,
            ['general']: [...existingMessages, optimisticMessage],
          },
        };
      });

      // Try WebSocket first, fallback to HTTP
      if (get().isConnected) {
        chatWebSocketService.sendMessage('general', {
          content: sanitizedContent,
          type: 'text',
        });
      } else {
        // HTTP fallback
        const message = await GeneralChatService.sendGeneralChatMessage({
          content: sanitizedContent,
          type: 'text',
        });

        // Replace optimistic message with real message
        set((state) => {
          const existingMessages = state.messages['general'] || [];
          const filteredMessages = existingMessages.filter(msg => !msg.isOptimistic);
          const messageExists = filteredMessages.some(existingMsg => existingMsg.id === message.id);

          if (messageExists) {
            console.log('ChatStore: Duplicate general chat message from HTTP fallback, skipping:', message.id);
            return state;
          }

          return {
            messages: {
              ...state.messages,
              ['general']: [...filteredMessages, message],
            },
          };
        });
      }
    } catch (error) {
      console.error('ChatStore: Failed to send general chat message:', error);
      
      // Remove optimistic message on error
      set((state) => {
        const existingMessages = state.messages['general'] || [];
        const filteredMessages = existingMessages.filter(msg => !msg.isOptimistic);
        return {
          messages: {
            ...state.messages,
            ['general']: filteredMessages,
          },
        };
      });
      
      throw error;
    }
  },

  /**
   * Mark general chat messages as read
   */
  markGeneralChatAsRead: async () => {
    try {
      // Try WebSocket first, fallback to HTTP
      if (get().isConnected) {
        chatWebSocketService.markAsRead('general');
      } else {
        await GeneralChatService.markGeneralChatAsRead();
      }

      // Update local unread count
      set((state) => ({
        generalChatUnreadCount: 0,
      }));
    } catch (error) {
      console.error('ChatStore: Failed to mark general chat as read:', error);
    }
  },


  /**
   * Reset store state
   */
  reset: () => {
    // Force disconnect to ensure cleanup
    chatWebSocketService.forceDisconnect();

    set({
      isConnected: false,
      isConnecting: false,
      connectionError: null,
      chats: [],
      chatsLoading: false,
      chatsError: null,
      chatsHasMore: true,
      chatsCursor: null,
      messages: {},
      messagesLoading: {},
      messagesError: {},
      messagesHasMore: {},
      messagesCursor: {},
      activeChatId: null,
      typingUsers: {},
      totalUnreadCount: 0,
    });
  },

  /**
   * Get connection status for debugging
   */
  getConnectionStatus: () => {
    return chatWebSocketService.getStatus();
  },
}));

export default useChatStore;

