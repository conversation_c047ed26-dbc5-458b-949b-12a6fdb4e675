import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import api from '../services/api/api';
import { useUserResources } from '../hooks/useUserResources';
import useUserDataStore from '../store/useUserDataStore';
import useAuthStore from '../store/useAuthStore';
import useChatStore from '../store/useChatStore';
import ChatInterface from './chat/ChatInterface.jsx';
import {
  Coins, 
  MessageCircleLu,
  MenuLu,
  HomeLu, 
  MapLu, 
  BriefcaseLu, 
  SwordLu, 
  PlaneLu, 
  FlagLu, 
  UserLu,
  ChevronDownLu,
  ChevronUpLu,
  LogOutLu
} from './svg/svgs.jsx';
import BalanceLogsModal from './balance/BalanceLogsModal.jsx';

export default function Navbar({ setMapBlurred }) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isMainMenuOpen, setIsMainMenuOpen] = useState(false);
  const navigate = useNavigate();
  const { money, gold, loading, refetch } = useUserResources();
  const { userData, fetchUserData, clearUserData } = useUserDataStore();
  const { logout } = useAuthStore();
  const totalUnreadCount = useChatStore(state => state.totalUnreadCount);
  const [isBalanceLogsModalOpen, setIsBalanceLogsModalOpen] = useState(false);
  const menuRef = useRef(null);
  const dropdownRef = useRef(null);
  const menuTimeoutRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Effect to blur map when any menu or chat is open
  useEffect(() => {
    if (setMapBlurred) {
      setMapBlurred((isMainMenuOpen && !isMobile) || isMenuOpen || isChatOpen);
    }
  }, [isMainMenuOpen, isMenuOpen, isChatOpen, setMapBlurred, isMobile]);

  useEffect(() => {
    fetchUserData(false);
    refetch();

    // Close menu when clicking outside
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target) && 
          dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsMainMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      if (menuTimeoutRef.current) clearTimeout(menuTimeoutRef.current);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await api.post('/auth/logout');
      clearUserData();
      logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const energyInPremium = (userData?.energy / 2);

  // Close menu when clicking a link
  const closeMenu = () => {
    setIsMainMenuOpen(false);
    setIsMenuOpen(false);
  };

  // Open menu on hover (desktop only)
  const handleMenuMouseEnter = () => {
    if (isMobile) return;
    if (menuTimeoutRef.current) clearTimeout(menuTimeoutRef.current);
    setIsMainMenuOpen(true);
  };

  // Close menu on mouse leave (desktop only)
  const handleMenuMouseLeave = () => {
    if (isMobile) return;
    menuTimeoutRef.current = setTimeout(() => {
      setIsMainMenuOpen(false);
    }, 300);
  };

  // Keep menu open when hovering over it (desktop only)
  const handleDropdownMouseEnter = () => {
    if (isMobile) return;
    if (menuTimeoutRef.current) clearTimeout(menuTimeoutRef.current);
    setIsMainMenuOpen(true);
  };

  // Close menu when mouse leaves (desktop only)
  const handleDropdownMouseLeave = () => {
    if (isMobile) return;
    menuTimeoutRef.current = setTimeout(() => {
      setIsMainMenuOpen(false);
    }, 200);
  };

  // Toggle main menu manually (desktop)
  const toggleDesktopMenu = () => {
    setIsMainMenuOpen(!isMainMenuOpen);
  };

  return (
    <nav className="bg-gradient-to-r from-gray-900 to-gray-900/80 border-b-2 border-gray-800 shadow-lg relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Left Section: Logo and Resources */}
          <div className="flex items-center space-x-4">
            {/* Logo */}
            <div className="flex items-center flex-shrink-0">
              <Link to="/home" className="flex items-center" onClick={closeMenu}>
                <img 
                  src="/wn-logo.png" 
                  alt="Warfront Nations Logo" 
                  className="h-16 py-1 transition-transform hover:scale-105" 
                  style={{ width: 'auto', filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.2))' }} 
                />
              </Link>
            </div>

            {/* Resources Display */}
            <div className="hidden md:flex items-center space-x-3">
              {/* Fixed size resource cards with exact dimensions */}
              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2 transition-all hover:border-yellow-500"
              onClick={() => setIsBalanceLogsModalOpen(true)}
              >
                <span className="text-yellow-400 text-lg">💰</span>
                <span className="text-gray-300 font-bold text-sm ml-2">
                  {loading ? "..." : userData?.money?.toLocaleString()}
                </span>
              </div>

              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2 transition-all hover:border-yellow-500"
              onClick={() => setIsBalanceLogsModalOpen(true)}
              >
                <span className="text-yellow-400"><Coins className="text-yellow-400 text-lg" /></span>
                <span className="text-gray-300 font-bold text-sm ml-2">
                  {loading ? "..." : userData?.gold?.toLocaleString()}
                </span>
              </div>

              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2 transition-all hover:border-blue-500">
                <span className="text-blue-400 text-lg">⚡</span>
                <div className="flex flex-col ml-2">
                  <div className="flex items-center">
                    <span className="text-gray-300 text-xs mr-1">Energy:</span>
                    <span className="text-gray-300 text-sm font-bold">{loading ? "..." : userData?.energy || 0}</span>
                  </div>
                  <div className="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden border border-gray-600">
                    {userData?.isPremium ? (
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
                        style={{ width: `${energyInPremium || 0}%` }}
                      />
                    ) : (
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
                        style={{ width: `${userData?.energy || 0}%` }}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Menu, Chat and Logout */}
          <div className="flex items-center space-x-3">
            {/* Chat Button */}
            <button
              onClick={() => setIsChatOpen(true)}
              className="relative text-gray-300 hover:text-white hover:bg-gray-800/50 p-3 rounded-lg flex items-center transition-all duration-200 group hover:shadow-lg hover:shadow-blue-500/20"
              title="Global Chat"
            >
              <MessageCircleLu className="w-5 h-5 group-hover:text-neonBlue transition-transform hover:scale-110" />
              {totalUnreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-600 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-lg shadow-red-900/30">
                  {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
                </span>
              )}
            </button>

            {/* Desktop Menu Dropdown (hidden on mobile) */}
            {!isMobile && (
              <div 
                className="relative hidden md:block" 
                ref={menuRef}
                onMouseEnter={handleMenuMouseEnter}
                onMouseLeave={handleMenuMouseLeave}
              >
                <button
                  onClick={toggleDesktopMenu}
                  className="text-gray-300 hover:text-white hover:bg-gray-800/50 px-4 py-3 rounded-lg text-sm font-medium flex items-center transition-all duration-200 group border border-transparent hover:border-neonBlue"
                >
                  <MenuLu className="w-5 h-5 mr-2 group-hover:text-neonBlue transition-colors" />
                  <span className="font-bold">Menu</span>
                  {isMainMenuOpen ? (
                    <ChevronUpLu className="w-4 h-4 ml-2 group-hover:text-neonBlue transition-transform" />
                  ) : (
                    <ChevronDownLu className="w-4 h-4 ml-2 group-hover:text-neonBlue transition-transform" />
                  )}
                </button>

                {/* Main Menu Dropdown Content */}
                {isMainMenuOpen && (
                  <div 
                    className="absolute right-0 mt-2 w-64 rounded-lg shadow-lg bg-gray-800 border border-gray-700 z-[1000] animate-fadeIn"
                    ref={dropdownRef}
                    onMouseEnter={handleDropdownMouseEnter}
                    onMouseLeave={handleDropdownMouseLeave}
                  >
                    <div className="py-2">
                      <Link
                        to="/home"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <HomeLu className="w-4 h-4 mr-3 text-neonBlue group-hover:scale-110" /> 
                        <span>Home</span>
                      </Link>

                      <Link
                        to="/map"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <MapLu className="w-4 h-4 mr-3 text-blue-400 group-hover:scale-110" /> 
                        <span>World Map</span>
                      </Link>

                      <Link
                        to="/jobs"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <BriefcaseLu className="w-4 h-4 mr-3 text-yellow-400 group-hover:scale-110" /> 
                        <span>Jobs & Career</span>
                      </Link>

                      <Link
                        to="/wars"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <SwordLu className="w-4 h-4 mr-3 text-red-500 group-hover:scale-110" /> 
                        <span>Wars</span>
                      </Link>

                      <Link
                        to="/travel/permissions"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <PlaneLu className="w-4 h-4 mr-3 text-blue-300 group-hover:scale-110" /> 
                        <span>Travel & Migration</span>
                      </Link>

                      <Link
                        to="/states/my-state"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <FlagLu className="w-4 h-4 mr-3 text-green-400 group-hover:scale-110" /> 
                        <span>My State</span>
                      </Link>

                      <Link
                        to="/profile"
                        className="text-gray-300 hover:text-white hover:bg-gray-700/50 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <UserLu className="w-4 h-4 mr-3 text-purple-400 group-hover:scale-110" /> 
                        <span>My Profile</span>
                      </Link>

                      <Link
                        to="/shop"
                        className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-900/20 block px-4 py-3 text-sm font-medium flex items-center transition-all group"
                        onClick={closeMenu}
                      >
                        <Coins className="w-4 h-4 text-yellow-400 mr-3 group-hover:scale-110" /> 
                        <span>Premium Shop</span>
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 px-4 py-3 rounded-lg text-sm font-medium flex items-center transition-all duration-200 group border border-transparent hover:border-red-500"
              title="Logout"
            >
              <LogOutLu className="w-5 h-5 group-hover:text-red-500 transition-colors" /> 
            </button>

            {/* Mobile menu button (visible only on mobile) */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-neonBlue hover:bg-gray-700 focus:outline-none transition-colors"
              >
                <span className="sr-only">Open main menu</span>
                {!isMenuOpen ? (
                  <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                ) : (
                  <svg className="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu (visible only on mobile) */}
      {isMenuOpen && (
        <div className="md:hidden bg-gradient-to-b from-gray-900 to-gray-900/80 animate-slideDown">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {/* Resources Display for Mobile */}
            <div className="flex justify-around py-4 border-b border-gray-800">
              {/* Fixed size resource cards with exact dimensions */}
              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2" 
              onClick={() => setIsBalanceLogsModalOpen(true)}
              >
                <span className="text-yellow-400">💰</span>
                <span className="text-gray-300 font-bold ml-2">{loading ? "..." : userData?.money?.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2"
              onClick={() => setIsBalanceLogsModalOpen(true)}
              >
                <span className="text-yellow-400"><Coins className="text-yellow-400" /></span>
                <span className="text-gray-300 font-bold ml-2">{loading ? "..." : userData?.gold?.toLocaleString()}</span>
              </div>
            </div>

            {/* Energy Display for Mobile */}
            <div className="flex justify-center py-4 border-b border-gray-800">
              <div className="flex items-center justify-center w-32 h-14 bg-gradient-to-r from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl px-3 py-2">
                <span className="text-blue-400">⚡</span>
                <div className="flex flex-col items-center ml-2">
                  <div className="flex items-center">
                    <span className="text-gray-300 text-xs mr-1">Energy:</span>
                    <span className="text-gray-300 font-bold text-sm">{loading ? "..." : userData?.energy || 0}</span>
                  </div>
                  <div className="w-16 h-1.5 bg-gray-700 rounded-full overflow-hidden border border-gray-600">
                    {userData?.isPremium ? (
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
                        style={{ width: `${energyInPremium || 0}%` }}
                      />
                    ) : (
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300"
                        style={{ width: `${userData?.energy || 0}%` }}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            <Link
              to="/home"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <HomeLu className="w-5 h-5 mr-3 text-neonBlue" /> Home
            </Link>

            <Link
              to="/map"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <MapLu className="w-5 h-5 mr-3 text-blue-400" /> Map
            </Link>

            <Link
              to="/jobs"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <BriefcaseLu className="w-5 h-5 mr-3 text-yellow-400" /> Jobs
            </Link>

            <Link
              to="/wars"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <SwordLu className="w-5 h-5 mr-3 text-red-500" /> Wars
            </Link>

            <Link
              to="/travel/permissions"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <PlaneLu className="w-5 h-5 mr-3 text-blue-400" /> Travel
            </Link>

            <Link
              to="/states/my-state"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <FlagLu className="w-5 h-5 mr-3 text-green-400" /> My State
            </Link>

            <Link
              to="/profile"
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <UserLu className="w-5 h-5 mr-3 text-purple-400" /> Profile
            </Link>

            <Link
              to="/shop"
              className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-900/20 block px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
              onClick={closeMenu}
            >
              <Coins className="w-5 h-5 text-yellow-400 mr-3" /> Shop
            </Link>

            {/* Chat Button - Mobile */}
            <button
              onClick={() => {
                setIsChatOpen(true);
                closeMenu();
              }}
              className="relative text-gray-300 hover:text-white hover:bg-gray-800/50 block w-full text-left px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
            >
              <MessageCircleLu className="w-5 h-5 mr-3 text-neonBlue" />
              Chat
              {totalUnreadCount > 0 && (
                <span className="ml-auto bg-red-600 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 shadow-lg shadow-red-900/30">
                  {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
                </span>
              )}
            </button>

            <button
              onClick={handleLogout}
              className="text-gray-300 hover:text-white hover:bg-gray-800/50 block w-full text-left px-4 py-3 rounded-lg text-base font-medium flex items-center transition-all"
            >
              <LogOutLu className="w-5 h-5 mr-3 text-red-500" /> Logout
            </button>
          </div>
        </div>
      )}

      {/* Chat Interface Modal */}
      <ChatInterface
        isOpen={isChatOpen}
        onClose={() => setIsChatOpen(false)}
        onCloseCallback={() => {
          if (setMapBlurred) {
            setMapBlurred((isMainMenuOpen && !isMobile) || isMenuOpen);
          }
        }}
      />

      {/* Global Styles */}
      <style>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
          z-index: 1000 !important;
        }
        @keyframes slideDown {
          from { max-height: 0; opacity: 0; }
          to { max-height: 1000px; opacity: 1; }
        }
        .animate-slideDown {
          animation: slideDown 0.5s ease-out forwards;
          overflow: hidden;
        }
        .group:hover .group-hover\\:scale-110 {
          transform: scale(1.1);
        }
      `}</style>
            <BalanceLogsModal
        isOpen={isBalanceLogsModalOpen}
        onClose={() => setIsBalanceLogsModalOpen(false)}
      />

    </nav>
  );
}

Navbar.propTypes = {
  setMapBlurred: PropTypes.func
};