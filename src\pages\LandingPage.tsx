import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { userService } from "../services/api/user.service";
import { regionService } from "../services/api/region.service";
import { stateService } from "../services/api/state.service";
import { warService } from "../services/api/war.service";
import {
  Users,
  MapPin,
  Flag,
  Sword,
  TrendingUp,
  Clock,
  Crown,
  Factory,
  Globe,
  Shield,
  Zap,
  Crosshair,
  Target,
} from "lucide-react";
import Footer from "../components/common/Footer";

interface GameStats {
  totalPlayers: number;
  totalRegions: number;
  totalStates: number;
  activeWars: number;
  totalWars: number;
  ongoingElections: number;
  totalPopulation: number;
}

interface WarTimelineEvent {
  eventType: string;
  description?: string;
  warName: string;
  timestamp: string | Date;
}

export default function LandingPage() {
  const [stats, setStats] = useState<GameStats>({
    totalPlayers: 0,
    totalRegions: 0,
    totalStates: 0,
    activeWars: 0,
    totalWars: 0,
    ongoingElections: 0,
    totalPopulation: 0,
  });
  const [recentWars, setRecentWars] = useState<WarTimelineEvent[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGameStats = async () => {
      try {
        setLoading(true);
        const [
          playersCount,
          regionsData,
          statesData,
          activeWarsData,
          globalWarStats,
          warTimeline,
        ] = await Promise.all([
          userService.getAllUsersCount().catch(() => 0),
          regionService.getAllRegionsCount().catch(() => 0),
          stateService.getAllStatesCount().catch(() => 0),
          warService.getActiveWars().catch(() => []),
          warService.getGlobalWarStats().catch(() => ({ totalWars: 0 })),
          warService.getWarTimeline(5).catch(() => []),
        ]);

        setStats({
          totalPlayers: playersCount,
          totalRegions: regionsData,
          totalStates: statesData,
          activeWars: activeWarsData.length,
          totalWars: globalWarStats.totalWars || 0,
          ongoingElections: 0,
          totalPopulation: playersCount,
        });

        setRecentWars(warTimeline);
      } catch (error) {
        console.error("Error fetching game statistics:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchGameStats();
  }, []);

  return (
    <>
      <div className="min-h-screen bg-[#0a0c1b] relative">
        {/* Hexagon Background Pattern */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute inset-0 bg-[url('/world.geojson')] opacity-5 bg-repeat"></div>
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 30 L15 0 L45 0 L60 30 L45 60 L15 60' fill='none' stroke='rgba(63, 135, 255, 0.1)' stroke-width='1'/%3E%3C/svg%3E")`,
              backgroundSize: "60px 60px",
            }}
          ></div>
        </div>

        {/* Hero Section */}
        <div className="relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 sm:pt-16 md:pt-20 pb-20 sm:pb-24 md:pb-32">
            <div className="text-center relative">
              {/* Targeting Reticle Animation */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-48 sm:w-64 h-48 sm:h-64 pointer-events-none">
                <div className="absolute inset-0 border-2 border-neonBlue/30 rounded-full animate-ping"></div>
                <div className="absolute inset-0 border border-neonBlue/20 rounded-full animate-pulse"></div>
              </div>

              {/* Logo with Sci-fi Frame */}
              <div className="relative inline-block mb-8 sm:mb-12">
                <div className="absolute -inset-4 border-2 border-neonBlue/30 rounded-lg transform -skew-x-12"></div>
                <div className="absolute -inset-4 bg-neonBlue/5 rounded-lg transform -skew-x-12"></div>
                <img
                  src="/wn-logo.png"
                  alt="Warfront Nations"
                  className="relative h-20 sm:h-24 md:h-32 w-auto"
                />
                <div className="absolute top-0 left-0 w-2 h-2 bg-neonBlue animate-pulse"></div>
                <div className="absolute top-0 right-0 w-2 h-2 bg-neonBlue animate-pulse"></div>
                <div className="absolute bottom-0 left-0 w-2 h-2 bg-neonBlue animate-pulse"></div>
                <div className="absolute bottom-0 right-0 w-2 h-2 bg-neonBlue animate-pulse"></div>
              </div>

              {/* Title with Tech Effect */}
              <h1 className="responsive-title font-black text-white mb-4 sm:mb-6 relative">
                <span className="relative">
                  <span className="absolute -inset-1 bg-neonBlue/20 blur-xl"></span>
                  WARFRONT NATIONS
                </span>
                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center opacity-50">
                  <div className="w-full h-1 bg-gradient-to-r from-transparent via-neonBlue to-transparent"></div>
                </div>
              </h1>

              <p className="responsive-subtitle text-gray-300 mb-8 sm:mb-12 max-w-3xl mx-auto leading-relaxed relative px-4">
                <span className="relative z-10">
                  Enter a world of strategic warfare, political intrigue, and
                  economic dominance. Build your empire, forge alliances, and
                  conquer territories in this immersive multiplayer strategy
                  game.
                </span>
              </p>

              {/* Game Features Tags */}
              <div className="flex flex-wrap justify-center gap-3 sm:gap-6 mb-8 sm:mb-12 px-4">
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-neonBlue to-neonGreen blur-sm opacity-50 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative px-3 sm:px-6 py-2 bg-gray-900/90 flex items-center gap-2 clip-path-polygon">
                    <Clock className="h-4 sm:h-5 w-4 sm:w-5 text-neonBlue" />
                    <span className="text-gray-300 text-sm sm:text-base whitespace-nowrap">
                      REAL-TIME COMBAT
                    </span>
                  </div>
                </div>
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-red-500 to-orange-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative px-6 py-2 bg-gray-900/90 flex items-center gap-2 clip-path-polygon">
                    <Users className="h-5 w-5 text-red-400" />
                    <span className="text-gray-300">MULTIPLAYER WAR</span>
                  </div>
                </div>
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-yellow-500 to-amber-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative px-6 py-2 bg-gray-900/90 flex items-center gap-2 clip-path-polygon">
                    <Crown className="h-5 w-5 text-yellow-400" />
                    <span className="text-gray-300">POLITICAL POWER</span>
                  </div>
                </div>
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-pink-500 blur-sm opacity-50 group-hover:opacity-100 transition-opacity"></div>
                  <div className="relative px-6 py-2 bg-gray-900/90 flex items-center gap-2 clip-path-polygon">
                    <Sword className="h-5 w-5 text-purple-400" />
                    <span className="text-gray-300">EPIC BATTLES</span>
                  </div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center px-4">
                <Link to="/register" className="w-full sm:w-auto">
                  <button className="w-full sm:w-auto group relative px-6 sm:px-8 py-3 sm:py-4 bg-neonBlue text-white rounded clip-path-polygon min-w-[200px] overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-neonBlue via-white to-neonBlue opacity-50 blur-xl group-hover:animate-pulse"></div>
                    <span className="relative font-bold tracking-wider text-base sm:text-lg">
                      ENLIST NOW
                    </span>
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-white/30 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform"></div>
                  </button>
                </Link>
                <Link to="/login" className="w-full sm:w-auto">
                  <button className="w-full sm:w-auto group relative px-6 sm:px-8 py-3 sm:py-4 bg-transparent text-neonBlue border-2 border-neonBlue rounded clip-path-polygon min-w-[200px]">
                    <span className="relative font-bold tracking-wider text-base sm:text-lg group-hover:text-white transition-colors">
                      RETURN TO BATTLE
                    </span>
                    <div className="absolute inset-0 bg-neonBlue transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform -z-10"></div>
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Game Statistics Section */}
        <div className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-neonBlue/5 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-10 sm:mb-16">
              <h2 className="text-3xl sm:text-4xl font-black text-white mb-4 tracking-wider">
                GLOBAL STATISTICS
              </h2>
              <div className="w-32 sm:w-40 h-1 bg-gradient-to-r from-transparent via-neonBlue to-transparent mx-auto"></div>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="relative">
                    <div className="absolute -inset-1 bg-neonBlue/20 blur-sm"></div>
                    <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon animate-pulse">
                      <div className="h-12 w-12 bg-gray-800 rounded-full mx-auto mb-4"></div>
                      <div className="h-8 w-32 bg-gray-800 mx-auto mb-2"></div>
                      <div className="h-4 w-24 bg-gray-800 mx-auto"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
                <div className="group relative">
                  <div className="absolute -inset-1 bg-neonBlue/20 blur-sm group-hover:bg-neonBlue/40 transition-colors"></div>
                  <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon transform group-hover:-translate-y-1 transition-transform">
                    <Users className="h-10 sm:h-12 w-10 sm:w-12 text-neonBlue mx-auto mb-4" />
                    <div className="text-2xl sm:text-3xl font-bold text-white mb-2 font-mono">
                      {stats.totalPlayers.toLocaleString()}
                    </div>
                    <div className="text-neonBlue font-bold tracking-wider text-sm sm:text-base">
                      ACTIVE PLAYERS
                    </div>
                  </div>
                </div>

                <div className="group relative">
                  <div className="absolute -inset-1 bg-neonGreen/20 blur-sm group-hover:bg-neonGreen/40 transition-colors"></div>
                  <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon transform group-hover:-translate-y-1 transition-transform">
                    <MapPin className="h-12 w-12 text-neonGreen mx-auto mb-4" />
                    <div className="text-3xl font-bold text-white mb-2 font-mono">
                      {stats.totalRegions.toLocaleString()}
                    </div>
                    <div className="text-neonGreen font-bold tracking-wider">
                      REGIONS
                    </div>
                  </div>
                </div>

                <div className="group relative">
                  <div className="absolute -inset-1 bg-yellow-400/20 blur-sm group-hover:bg-yellow-400/40 transition-colors"></div>
                  <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon transform group-hover:-translate-y-1 transition-transform">
                    <Flag className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
                    <div className="text-3xl font-bold text-white mb-2 font-mono">
                      {stats.totalStates.toLocaleString()}
                    </div>
                    <div className="text-yellow-400 font-bold tracking-wider">
                      STATES
                    </div>
                  </div>
                </div>

                <div className="group relative">
                  <div className="absolute -inset-1 bg-red-500/20 blur-sm group-hover:bg-red-500/40 transition-colors"></div>
                  <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon transform group-hover:-translate-y-1 transition-transform">
                    <Sword className="h-12 w-12 text-red-500 mx-auto mb-4" />
                    <div className="text-3xl font-bold text-white mb-2 font-mono">
                      {stats.activeWars.toLocaleString()}
                    </div>
                    <div className="text-red-500 font-bold tracking-wider">
                      ACTIVE WARS
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Game Features Section */}
        <div className="relative py-12 sm:py-16 md:py-20">
          <div className="absolute inset-0 bg-gradient-to-t from-transparent via-neonGreen/5 to-transparent"></div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-10 sm:mb-16">
              <h2 className="text-3xl sm:text-4xl font-black text-white mb-4 tracking-wider">
                WARFARE FEATURES
              </h2>
              <div className="w-32 sm:w-40 h-1 bg-gradient-to-r from-transparent via-neonGreen to-transparent mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
              <div className="group relative">
                <div className="absolute -inset-1 bg-red-500/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-6 sm:p-8 clip-path-polygon border border-gray-800 group-hover:border-red-500/50 transition-colors">
                  <div className="relative w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6">
                    <div className="absolute inset-0 bg-red-500/20 rounded-full animate-ping"></div>
                    <Sword className="h-12 w-12 sm:h-16 sm:w-16 text-red-500 relative z-10" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold text-white mb-3 sm:mb-4 tracking-wider">
                    STRATEGIC WARFARE
                  </h3>
                  <p className="text-gray-400 leading-relaxed text-sm sm:text-base">
                    Command armies across land and sea. Deploy tactical strikes,
                    manage military resources, and lead your forces to glorious
                    victory.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute -inset-1 bg-yellow-400/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-8 clip-path-polygon border border-gray-800 group-hover:border-yellow-400/50 transition-colors">
                  <div className="relative w-16 h-16 mx-auto mb-6">
                    <div className="absolute inset-0 bg-yellow-400/20 rounded-full animate-ping"></div>
                    <Crown className="h-16 w-16 text-yellow-400 relative z-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 tracking-wider">
                    POLITICAL DOMINANCE
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    Rise to power through political intrigue. Win elections,
                    govern states, and shape the future of nations through
                    strategic leadership.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute -inset-1 bg-neonGreen/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-8 clip-path-polygon border border-gray-800 group-hover:border-neonGreen/50 transition-colors">
                  <div className="relative w-16 h-16 mx-auto mb-6">
                    <div className="absolute inset-0 bg-neonGreen/20 rounded-full animate-ping"></div>
                    <Factory className="h-16 w-16 text-neonGreen relative z-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 tracking-wider">
                    ECONOMIC EMPIRE
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    Build a powerful economic engine. Establish factories,
                    control vital resources, and dominate the global markets.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute -inset-1 bg-neonBlue/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-8 clip-path-polygon border border-gray-800 group-hover:border-neonBlue/50 transition-colors">
                  <div className="relative w-16 h-16 mx-auto mb-6">
                    <div className="absolute inset-0 bg-neonBlue/20 rounded-full animate-ping"></div>
                    <Shield className="h-16 w-16 text-neonBlue relative z-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 tracking-wider">
                    ALLIANCE SYSTEM
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    Forge powerful alliances and engage in complex diplomacy.
                    Your network of allies can make the difference between
                    victory and defeat.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute -inset-1 bg-purple-500/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-8 clip-path-polygon border border-gray-800 group-hover:border-purple-500/50 transition-colors">
                  <div className="relative w-16 h-16 mx-auto mb-6">
                    <div className="absolute inset-0 bg-purple-500/20 rounded-full animate-ping"></div>
                    <Globe className="h-16 w-16 text-purple-500 relative z-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 tracking-wider">
                    WORLD CONQUEST
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    Expand your influence across the globe. Control strategic
                    territories and establish your dominion over vast regions.
                  </p>
                </div>
              </div>

              <div className="group relative">
                <div className="absolute -inset-1 bg-green-500/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative bg-gray-900/90 p-8 clip-path-polygon border border-gray-800 group-hover:border-green-500/50 transition-colors">
                  <div className="relative w-16 h-16 mx-auto mb-6">
                    <div className="absolute inset-0 bg-green-500/20 rounded-full animate-ping"></div>
                    <Target className="h-16 w-16 text-green-500 relative z-10" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4 tracking-wider">
                    SKILL MASTERY
                  </h3>
                  <p className="text-gray-400 leading-relaxed">
                    Develop your military expertise and leadership abilities.
                    Unlock powerful skills and become a legendary commander.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Wars Section */}
        {recentWars.length > 0 && (
          <div className="relative py-12 sm:py-16 md:py-20">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-red-500/5 to-transparent"></div>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-10 sm:mb-16">
                <h2 className="text-3xl sm:text-4xl font-black text-white mb-4 tracking-wider">
                  BATTLE REPORTS
                </h2>
                <div className="w-32 sm:w-40 h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent mx-auto"></div>
              </div>

              <div className="space-y-4 sm:space-y-6 max-w-4xl mx-auto">
                {recentWars.slice(0, 5).map((event, index) => (
                  <div key={index} className="group relative">
                    <div className="absolute -inset-1 bg-red-500/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="relative bg-gray-900/90 p-4 sm:p-6 clip-path-polygon border border-gray-800 group-hover:border-red-500/50 transition-colors">
                      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-0">
                        <div className="flex items-center space-x-4 sm:space-x-6">
                          <div className="relative">
                            <div className="absolute inset-0 bg-red-500/20 rounded-full animate-ping"></div>
                            <div className="relative bg-gray-800 p-2 sm:p-3 rounded-full">
                              <Crosshair className="h-5 w-5 sm:h-6 sm:w-6 text-red-500" />
                            </div>
                          </div>
                          <div>
                            <div className="text-lg sm:text-xl font-bold text-white tracking-wider">
                              {event.warName}
                            </div>
                            <div className="text-gray-400 mt-1 text-sm sm:text-base">
                              {event.description}
                            </div>
                          </div>
                        </div>
                        <div className="text-red-500 font-mono text-sm sm:text-base">
                          {new Date(event.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Call to Action Section */}
        <div className="relative py-12 sm:py-16 md:py-20 overflow-hidden">
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-b from-transparent to-neonBlue/10"></div>
            <div className="absolute inset-0 bg-[url('/world.geojson')] opacity-5 bg-center bg-no-repeat bg-contain"></div>
          </div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl sm:text-5xl font-black text-white mb-6 sm:mb-8 tracking-wider">
              YOUR LEGEND AWAITS
            </h2>
            <p className="text-lg sm:text-xl text-gray-300 mb-8 sm:mb-12 leading-relaxed">
              Join the ranks of thousands of commanders in the ultimate battle
              for supremacy. Your path to glory begins now.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center">
              <Link to="/register" className="w-full sm:w-auto">
                <button className="w-full sm:w-auto group relative px-8 sm:px-12 py-4 sm:py-5 bg-gradient-to-r from-neonBlue to-neonGreen text-white rounded clip-path-polygon min-w-[200px] sm:min-w-[250px]">
                  <span className="relative text-lg sm:text-xl font-black tracking-widest">
                    BEGIN CONQUEST
                  </span>
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-white/30 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform"></div>
                </button>
              </Link>
              <Link to="/login" className="w-full sm:w-auto">
                <button className="w-full sm:w-auto group relative px-8 sm:px-12 py-4 sm:py-5 bg-transparent text-white border-2 border-neonBlue rounded clip-path-polygon min-w-[200px] sm:min-w-[250px]">
                  <span className="relative text-lg sm:text-xl font-black tracking-widest">
                    REJOIN BATTLE
                  </span>
                  <div className="absolute inset-0 bg-neonBlue/20 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform -z-10"></div>
                </button>
              </Link>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    </>
  );
}
