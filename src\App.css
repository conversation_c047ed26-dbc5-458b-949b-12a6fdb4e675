#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Game UI Styles */
.clip-path-polygon {
  clip-path: polygon(0 0, 100% 0, 98% 100%, 2% 100%);
}

@media (max-width: 640px) {
  .clip-path-polygon {
    clip-path: polygon(0 0, 100% 0, 97% 100%, 3% 100%);
  }
}

/* Responsive Typography */
.responsive-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
}

.responsive-subtitle {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
  line-height: 1.5;
}

/* Game Card Animations */
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}
