import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { stateService } from '../services/api/state.service';
import Navbar from '../components/Navbar';
import { generateStateColor } from '../utils/colorGenerator';
import { useAuthGuard } from '../hooks/useAuthGuard';

const stateColorMap = new Map();

export default function MapPage() {
  useAuthGuard();
  const [states, setStates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [geoJsonData, setGeoJsonData] = useState(null);
  const [isBlurred, setIsBlurred] = useState(false);

  useEffect(() => {
    const fetchGeoJson = async () => {
      try {
        const response = await fetch('/world.geojson');
        if (!response.ok) throw new Error('Failed to fetch GeoJSON');
        const data = await response.json();
        setGeoJsonData(data);
      } catch (error) {
        console.error('Error fetching GeoJSON:', error);
        try {
          const response = await fetch('https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson');
          const data = await response.json();
          setGeoJsonData(data);
        } catch (fallbackError) {
          console.error('Fallback GeoJSON also failed:', fallbackError);
        }
      }
    };

    fetchGeoJson();
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const states = await stateService.getAllStates();
        setStates(states);
      } catch (err) {
        setError('Failed to fetch map data');
        console.error('Error fetching map data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStates();
  }, []);

  const getStateColor = (stateId) => {
    if (!stateColorMap.has(stateId)) {
      stateColorMap.set(stateId, generateStateColor(stateColorMap.size));
    }
    return stateColorMap.get(stateId);
  };

  const findStateAndRegionByCountryName = (countryName) => {
    for (const state of states) {
      const region = state.regions.find(r => r.countryCode === countryName);
      if (region) {
        return { state, region };
      }
    }
    return null;
  };

  const getGeoJSONStyle = (feature) => {
    const countryName = feature.id || feature.properties['name'];
    const stateAndRegion = findStateAndRegionByCountryName(countryName);

    return {
      fillColor: stateAndRegion ? getStateColor(stateAndRegion.state.id) : '#2d3748',
      weight: 1,
      opacity: 1,
      color: '#000000',
      fillOpacity: stateAndRegion ? 0.8 : 0.3
    };
  };

  const onEachFeature = (feature, layer) => {
    const countryId = feature.id || feature.properties['name'];
    const countryName = feature.properties['name'] || "Unknown";
    const stateAndRegion = findStateAndRegionByCountryName(countryId);

    if (stateAndRegion) {
      const { state, region } = stateAndRegion;

      layer.bindPopup(`
        <div class="p-3 min-w-[200px]">
          <h3 class="font-bold text-lg text-gray-900 mb-2">${region.name}</h3>
          <div class="w-full h-2 mb-2 rounded" style="background-color: ${getStateColor(state.id)}"></div>
          <div class="text-sm text-gray-700 space-y-1">
            <div><strong>State:</strong> ${state.name}</div>
            <div><strong>Population:</strong> ${region.population?.toLocaleString() || 'Unknown'}</div>
          </div>
          <div class="mt-3">
            <a href="/states/${state.id}" style="color:#fff" class="inline-block bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
              View Details
            </a>
          </div>
        </div>
      `);

      layer.on({
        mouseover: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 2,
            fillOpacity: 0.9,
            color: '#ffffff'
          });
        },
        mouseout: (e) => {
          const layer = e.target;
          layer.setStyle({
            weight: 1,
            fillOpacity: 0.8,
            color: '#000000'
          });
        }
      });
    } else {
      layer.bindPopup(`
        <div class="p-3">
          <h3 class="font-bold text-lg text-gray-900">${countryName}</h3>
          <p class="text-gray-600 text-sm mt-1">Unclaimed Territory</p>
        </div>
      `);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar setMapBlurred={setIsBlurred} />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading map data...</div>
        </div>
      </div>
    );
  }

  if (error) return (
    <div className="min-h-screen bg-gray-900">
      <Navbar setMapBlurred={setIsBlurred} />
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-900 overflow-hidden">
      <Navbar setMapBlurred={setIsBlurred} />
      <div className="h-[calc(100vh-5rem)] relative">
        {isBlurred && (
          <div className="absolute inset-0 z-40 bg-black/50 backdrop-blur-sm transition-all duration-300" />
        )}
        
        <div className={`h-full w-full transition-all duration-300 ${isBlurred ? 'blur-sm' : ''}`}>
          <MapContainer
            center={[20, 0]}
            zoom={2}
            style={{ 
              height: '100%', 
              width: '100%',
              pointerEvents: isBlurred ? 'none' : 'auto'
            }}
            minZoom={2}
            maxZoom={6}
            maxBounds={[[-85, -180], [85, 180]]}
          >
            <TileLayer
              url="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
              attribution=""
            />

            {geoJsonData && (
              <GeoJSON
                data={geoJsonData}
                style={getGeoJSONStyle}
                onEachFeature={onEachFeature}
              />
            )}
          </MapContainer>
        </div>
      </div>
    </div>
  );
}