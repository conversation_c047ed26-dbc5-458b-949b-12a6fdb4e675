import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';
import { warService } from '../../services/api/war.service';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';
import useUserDataStore from '../../store/useUserDataStore';
import { formatDate } from '../../utils/formatDate';
import WarParticipateForm from './WarParticipateForm';
import RegionWarHistory from '../analytics/RegionWarHistory';
import RegionalPerformance from '../analytics/RegionalPerformance';
import BackArrowIcon from '../svg/BackArrowIcon';
import ErrorIcon from '../svg/ErrorIcon';
import CloseIcon from '../svg/CloseIcon';
import ExternalLinkIcon from '../svg/ExternalLinkIcon';

const WarDetail = () => {
  const { id } = useParams();
  const [war, setWar] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [participating, setParticipating] = useState(false);
  const [participationLoading, setParticipationLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const { userData, fetchUserData } = useUserDataStore();

  const [showModal, setShowModal] = useState(false);
  const [modalSide, setModalSide] = useState(null);
  const [userCurrentSide, setUserCurrentSide] = useState(null);

  const fetchUserCurrentSide = async () => {
    if (!war || war.warType !== 'revolution') return;
    if (userCurrentSide) return;
    try {
      const response = await warService.getUserSideInRevolution(id);
      setUserCurrentSide(response.side);
    } catch (error) {
      console.error('Failed to fetch user side:', error);
      const isInAttackers = war.participants?.attackers?.some(p => p.userId === userData.id);
      const isInDefenders = war.participants?.defenders?.some(p => p.userId === userData.id);

      if (isInAttackers && !isInDefenders) setUserCurrentSide('attacker');
      else if (isInDefenders && !isInAttackers) setUserCurrentSide('defender');
      else setUserCurrentSide(null);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [warData, myWars] = await Promise.all([
          warService.getWar(id),
          warService.getMyWars()
        ]);
        setWar(warData);
        const isParticipating = myWars.some(w => w.id === warData.id);
        setParticipating(isParticipating);
        setError(null);
      } catch (err) {
        console.error('Error fetching war details:', err);
        setError('Failed to load war details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [id]);

  useEffect(() => {
    const fetchSide = async () => {
      if (!war || !participating || war.warType !== 'revolution' || !userData) return;
      if (userCurrentSide !== null) return;

      const isInAttackers = war.participants?.attackers?.some(p => p.userId === userData.id);
      const isInDefenders = war.participants?.defenders?.some(p => p.userId === userData.id);

      if (isInAttackers && isInDefenders) {
        await fetchUserCurrentSide();
      } else if (isInAttackers) {
        setUserCurrentSide('attacker');
      } else if (isInDefenders) {
        setUserCurrentSide('defender');
      }
    };
    fetchSide();
  }, [war, participating, userData, userCurrentSide]);

  const handleJoinWar = async (isAttacker) => {
    if (participating) return;
    try {
      setParticipationLoading(true);
      await fetchUserData(true);
      const latestEnergy = userData?.energy || 0;
      if (latestEnergy <= 0) {
        showErrorToast('You do not have enough energy to participate in the war.');
        return;
      }
      if (war.warType === 'revolution') {
        const side = isAttacker ? 'attacker' : 'defender';
        await setUserCurrentSide(side);
      }
      showSuccessToast(`Successfully joined the war as ${isAttacker ? 'attacker' : 'defender'}!`);
      await fetchUserData(true);
      const updatedWar = await warService.getWar(id);
      setWar(updatedWar);
      setParticipating(true);
      setActiveTab('participate');
    } catch (err) {
      console.error('Error joining war:', err);
      showErrorToast(err);
      setError('Failed to join the war. Please try again later.');
    } finally {
      setParticipationLoading(false);
    }
  };

  const handleParticipationComplete = async () => {
    try {
      const updatedWar = await warService.getWar(id);
      setWar(updatedWar);
      await fetchUserData(true);
    } catch (err) {
      console.error('Error refreshing war data:', err);
      showErrorToast('Failed to refresh war data');
    }
  };

  const handleSwitchSide = async (newSide) => {
    if (war?.warType !== 'revolution' || newSide === getUserSideInRevolution()) return;
    try {
      setParticipationLoading(true);
      await fetchUserData(true);
      const latestEnergy = userData?.energy || 0;
      if (latestEnergy <= 0) {
        showErrorToast('You need energy to switch sides.');
        return;
      }
      const participateData = {
        energyAmount: latestEnergy,
        autoMode: false,
        side: newSide
      };
      await warService.participateInWar(id, participateData);
      showSuccessToast(`Successfully switched to ${newSide} side!`);
      setUserCurrentSide(newSide);
      await fetchUserData(true);
      const updatedWar = await warService.getWar(id);
      setWar(updatedWar);
    } catch (err) {
      console.error('Error switching sides:', err);
      showErrorToast(err);
    } finally {
      setParticipationLoading(false);
    }
  };

  if (loading) return (
    <div className="flex flex-col items-center justify-center min-h-[50vh]">
      <div className="relative">
        <div className="w-16 h-16 border-t-2 border-b-2 border-red-600 rounded-full animate-spin"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 bg-red-600 rounded-full animate-ping"></div>
        </div>
      </div>
      <span className="mt-6 text-xl font-bold text-white tracking-wider">
        GATHERING BATTLEFIELD INTELLIGENCE...
      </span>
    </div>
  );
  
  if (error) return (
    <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-red-100 p-6 rounded-xl mt-8 shadow-lg">
      <div className="flex items-start">
        <ErrorIcon className="w-8 h-8 text-red-400 mr-3 flex-shrink-0" />
        <div>
          <p className="font-black text-lg">COMMUNICATION FAILURE</p>
          <p className="mt-2">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="mt-4 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 px-4 py-2 rounded-md font-bold text-white text-sm"
          >
            RETRY TRANSMISSION
          </button>
        </div>
      </div>
    </div>
  );
  
  if (!war) return <div className="text-white text-center mt-8">War not found</div>;

  const attackers = war.participants?.attackers || [];
  const defenders = war.participants?.defenders || [];

  const getProgressPercentage = () => {
    const attackerDamage = (war.attackerGroundDamage || war.attackerSeaDamage || 0);
    const defenderDamage = (war.defenderGroundDamage || war.defenderSeaDamage || 0) + (war.damageRequirement || 0);
    const total = attackerDamage + defenderDamage || 1;
    return (attackerDamage / total) * 100;
  };

  const isWarActive = (status) => {
    const activeStatuses = ['active'];
    return activeStatuses.includes(status);
  };

  const getUserSideInRevolution = () => {
    if (!war || war.warType !== 'revolution' || !userData) return null;
    if (userCurrentSide !== null) return userCurrentSide;
    const isInAttackers = war.participants?.attackers?.some(p => p.userId === userData.id);
    const isInDefenders = war.participants?.defenders?.some(p => p.userId === userData.id);
    if (isInAttackers && !isInDefenders) return 'attacker';
    if (isInDefenders && !isInAttackers) return 'defender';
    return null;
  };
  
  const consolidateParticipants = (participants) => {
    const consolidated = {};
    participants.forEach(participant => {
      const key = participant.userId
        ? `user-${participant.userId}`
        : participant.state
          ? `state-${participant.state.id}`
          : `unknown-${participant.id}`;
      if (!consolidated[key]) {
        consolidated[key] = {
          ...participant,
          totalDamage: participant.damage || 0,
          count: 1
        };
      } else {
        consolidated[key].totalDamage += (participant.damage || 0);
        consolidated[key].count += 1;
      }
    });
    return Object.values(consolidated).sort((a, b) => b.totalDamage - a.totalDamage);
  };

  const consolidatedAttackers = attackers ? consolidateParticipants(attackers) : [];
  const consolidatedDefenders = defenders ? consolidateParticipants(defenders) : [];
  
  const getWarTypeColor = (warType) => {
    if (!warType) return {
      border: 'border-purple-500',
      badge: 'bg-gradient-to-r from-purple-600 to-purple-700',
      glow: 'shadow-purple-500/20',
      damageBar: 'bg-gradient-to-r from-purple-500 to-purple-600',
      displayName: 'WAR'
    };
    switch (warType) {
      case 'sea':
        return {
          border: 'border-blue-500',
          badge: 'bg-gradient-to-r from-blue-600 to-blue-700',
          glow: 'shadow-blue-500/20',
          damageBar: 'bg-gradient-to-r from-blue-500 to-blue-600',
          displayName: 'SEA WAR'
        };
      case 'ground':
        return {
          border: 'border-green-500',
          badge: 'bg-gradient-to-r from-green-600 to-green-700',
          glow: 'shadow-green-500/20',
          damageBar: 'bg-gradient-to-r from-green-500 to-green-600',
          displayName: 'GROUND WAR'
        };
      case 'revolution':
        return {
          border: 'border-yellow-500',
          badge: 'bg-gradient-to-r from-yellow-600 to-yellow-700',
          glow: 'shadow-yellow-500/20',
          damageBar: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
          displayName: 'REVOLUTION'
        };
      default:
        return {
          border: 'border-purple-500',
          badge: 'bg-gradient-to-r from-purple-600 to-purple-700',
          glow: 'shadow-purple-500/20',
          damageBar: 'bg-gradient-to-r from-purple-500 to-purple-600',
          displayName: 'WAR'
        };
    }
  };

  const warTypeColor = getWarTypeColor(war.warType);
  
  const renderDamageIndicator = (attackerDamage = 0, defenderDamage = 0) => {
    const totalDamage = attackerDamage + defenderDamage || 1;
    const attackerPercentage = Math.round((attackerDamage / totalDamage) * 100);
    return (
      <div className="relative">
        <div className="flex justify-between text-xs mb-1">
          <span className="font-bold text-gray-400">ATTACKER DMG: {attackerDamage}</span>
          <span className="font-bold text-gray-400">DEFENDER DMG: {defenderDamage}</span>
        </div>
        <div className="w-full bg-gray-700 h-2.5 rounded-full overflow-hidden border border-gray-600">
          <div 
            className="bg-gradient-to-r from-red-700 to-red-900 h-full"
            style={{ width: `${attackerPercentage}%` }}
          ></div>
        </div>
        <div className="absolute inset-0 flex justify-between px-1">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="w-px h-2.5 bg-gray-800"></div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4 relative">
      <div className="mb-6">
        <Link to="/wars" className="inline-flex items-center gap-1 text-neonBlue hover:text-blue-400">
          <BackArrowIcon className="h-5 w-5" />
          Back to Wars
        </Link>
      </div>

      <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <div className="flex items-center gap-3 mb-2">
              <h2 className="text-3xl md:text-4xl font-black text-white uppercase tracking-wide">
                {warTypeColor.displayName}
              </h2>
              <span className={`px-3 py-1 rounded-lg text-xs font-black uppercase ${warTypeColor.badge}`}>
                {war.warType?.toUpperCase()?.slice(0, 3) || 'WAR'}
              </span>
            </div>
            <p className="text-gray-400 mt-1">
              Strategic overview of this conflict
            </p>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6">
          <nav className="flex flex-wrap gap-2">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-2 rounded-xl font-bold transition-colors ${
                activeTab === 'overview' 
                  ? 'bg-gradient-to-r from-red-700 to-red-800 text-white' 
                  : 'bg-gradient-to-r from-gray-800 to-gray-900 text-gray-400 hover:text-white'
              } border-2 border-gray-700`}
            >
              Overview
            </button>
            {participating && isWarActive(war.status) && (
              <button
                onClick={() => setActiveTab('participate')}
                className={`px-4 py-2 rounded-xl font-bold transition-colors ${
                  activeTab === 'participate' 
                    ? 'bg-gradient-to-r from-red-700 to-red-800 text-white' 
                    : 'bg-gradient-to-r from-gray-800 to-gray-900 text-gray-400 hover:text-white'
                } border-2 border-gray-700`}
              >
                Fight
              </button>
            )}
            <button
              onClick={() => setActiveTab('history')}
              className={`px-4 py-2 rounded-xl font-bold transition-colors ${
                activeTab === 'history' 
                  ? 'bg-gradient-to-r from-red-700 to-red-800 text-white' 
                  : 'bg-gradient-to-r from-gray-800 to-gray-900 text-gray-400 hover:text-white'
              } border-2 border-gray-700`}
            >
              Region History
            </button>
            <button
              onClick={() => setActiveTab('performance')}
              className={`px-4 py-2 rounded-xl font-bold transition-colors ${
                activeTab === 'performance' 
                  ? 'bg-gradient-to-r from-red-700 to-red-800 text-white' 
                  : 'bg-gradient-to-r from-gray-800 to-gray-900 text-gray-400 hover:text-white'
              } border-2 border-gray-700`}
            >
              Performance
            </button>
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
                <h2 className="text-xl font-bold text-white mb-4 border-b border-gray-700 pb-2">War Details</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Target:</span>
                    <span className="text-white font-medium">{war.warTarget}</span>
                  </div>
                  {war.region && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Target Region:</span>
                      <Link to={`/regions/${war.region.id}`} className="text-neonBlue hover:text-blue-400">
                        {war.region.name}
                      </Link>
                    </div>
                  )}
                  {war.state && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Target State:</span>
                      <Link to={`/states/${war.state.id}`} className="text-neonBlue hover:text-blue-400">
                        {war.state.name}
                      </Link>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Started:</span>
                    <span>{formatDate(war.declaredAt)}</span>
                  </div>
                  {war.endedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Ended:</span>
                      <span>{formatDate(war.endedAt)}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
                <h2 className="text-xl font-bold text-white mb-4 border-b border-gray-700 pb-2">War Progress</h2>
                {renderDamageIndicator(
                  (war.attackerGroundDamage || 0) + (war.attackerSeaDamage || 0),
                  (war.defenderGroundDamage || 0) + (war.defenderSeaDamage || 0) + (war.damageRequirement || 0)
                )}
                <div className="flex justify-between text-xs mt-1 text-gray-400">
                  <span>Attacker Victory</span>
                  <span>Defender Victory</span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-white">Attackers ({consolidatedAttackers.length})</h2>
                  <div className="bg-red-900 text-red-100 text-xs px-2 py-1 rounded-full">
                    {(war.attackerGroundDamage || 0) + (war.attackerSeaDamage || 0)} DMG
                  </div>
                </div>
                
                {/* رابط المنطقة للمهاجمين */}
                {war.attackerRegion && (
                  <div className="mb-3 flex items-center text-sm text-gray-400">
                    <span className="mr-2">Region:</span>
                    <Link 
                      to={`/regions/${war.attackerRegion.id}`} 
                      className="text-neonBlue hover:text-blue-400 flex items-center"
                    >
                      {war.attackerRegion.name}
                      <ExternalLinkIcon className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                )}
                
                {consolidatedAttackers.length === 0 ? (
                  <p className="text-gray-400 text-center py-4">No attackers yet</p>
                ) : (
                  <div>
                    <ul className="space-y-3">
                      {consolidatedAttackers.slice(0, 5).map(participant => (
                        <li key={participant.id} className="bg-gray-800/50 p-3 rounded-lg flex justify-between items-center">
                          <div>
                            {participant.userId && war.status !== 'pending' ? (
                              <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                                {participant?.username}
                              </Link>
                            ) : participant.state ? (
                              <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                                {participant.state.name}
                              </Link>
                            ) : (
                              <span className="text-gray-300">Unknown participant</span>
                            )}
                            {participant.count > 1 && (
                              <span className="ml-2 text-xs text-gray-400">
                                ({participant.count} attacks)
                              </span>
                            )}
                          </div>
                          {participant.totalDamage > 0 && (
                            <span className="text-sm text-gray-300 bg-red-900/50 px-2 py-1 rounded">
                              {participant.totalDamage.toLocaleString()}
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                    {consolidatedAttackers.length > 5 && (
                      <div className="mt-4 text-center">
                        <button
                          onClick={() => {
                            setModalSide('attackers');
                            setShowModal(true);
                          }}
                          className="text-neonBlue hover:text-blue-400 text-sm"
                        >
                          View all {consolidatedAttackers.length} attackers...
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-white">Defenders ({consolidatedDefenders.length})</h2>
                  <div className="bg-blue-900 text-blue-100 text-xs px-2 py-1 rounded-full">
                    {(war.defenderGroundDamage || 0) + (war.defenderSeaDamage || 0)} DMG
                  </div>
                </div>
                
                {/* رابط المنطقة للمدافعين */}
                {war.defenderRegion && (
                  <div className="mb-3 flex items-center text-sm text-gray-400">
                    <span className="mr-2">Region:</span>
                    <Link 
                      to={`/regions/${war.defenderRegion.id}`} 
                      className="text-neonBlue hover:text-blue-400 flex items-center"
                    >
                      {war.defenderRegion.name}
                      <ExternalLinkIcon className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                )}
                
                {consolidatedDefenders.length === 0 ? (
                  <p className="text-gray-400 text-center py-4">No defenders yet</p>
                ) : (
                  <div>
                    <ul className="space-y-3">
                      {consolidatedDefenders.slice(0, 5).map(participant => (
                        <li key={participant.id} className="bg-gray-800/50 p-3 rounded-lg flex justify-between items-center">
                          <div>
                            {participant.userId ? (
                              <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                                {participant?.username}
                              </Link>
                            ) : participant.state ? (
                              <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                                {participant.state.name}
                              </Link>
                            ) : (
                              <span className="text-gray-300">Unknown participant</span>
                            )}
                            {participant.count > 1 && (
                              <span className="ml-2 text-xs text-gray-400">
                                ({participant.count} attacks)
                              </span>
                            )}
                          </div>
                          {participant.totalDamage > 0 && (
                            <span className="text-sm text-gray-300 bg-blue-900/50 px-2 py-1 rounded">
                              {participant.totalDamage.toLocaleString()}
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                    {consolidatedDefenders.length > 5 && (
                      <div className="mt-4 text-center">
                        <button
                          onClick={() => {
                            setModalSide('defenders');
                            setShowModal(true);
                          }}
                          className="text-neonBlue hover:text-blue-400 text-sm"
                        >
                          View all {consolidatedDefenders.length} defenders...
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {isWarActive(war.status) && !participating && (
              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-6 mb-8">
                <h2 className="text-xl font-bold text-white mb-4">Join the Battle</h2>
                {war.warType === 'revolution' ? (
                  <div className="text-center">
                    <p className="text-yellow-400 mb-4">Choose your side in this revolution:</p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <button
                        onClick={() => handleJoinWar(true)}
                        disabled={participationLoading}
                        className="px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 rounded-xl font-bold text-white disabled:opacity-50"
                      >
                        Fight for Attacking Side
                      </button>
                      <button
                        onClick={() => handleJoinWar(false)}
                        disabled={participationLoading}
                        className="px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 rounded-xl font-bold text-white disabled:opacity-50"
                      >
                        Fight For Defending Side
                      </button>
                    </div>
                  </div>
                ) : userData?.region ? (
                  (() => {
                    const isAttackerRegion = war.attackerRegion && userData.region.id === war.attackerRegion.id;
                    const isDefenderRegion = war.defenderRegion && userData.region.id === war.defenderRegion.id;
                    if (isAttackerRegion) {
                      return (
                        <button
                          onClick={() => handleJoinWar(true)}
                          disabled={participationLoading}
                          className="w-full px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 rounded-xl font-bold text-white disabled:opacity-50"
                        >
                          Join War as Attacker
                        </button>
                      );
                    } else if (isDefenderRegion) {
                      return (
                        <button
                          onClick={() => handleJoinWar(false)}
                          disabled={participationLoading}
                          className="w-full px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 rounded-xl font-bold text-white disabled:opacity-50"
                        >
                          Join War as Defender
                        </button>
                      );
                    } else {
                      return (
                        <div className="text-center">
                          <p className="text-yellow-400 mb-4">Your region is not directly involved in this war.</p>
                          <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button
                              onClick={() => handleJoinWar(true)}
                              disabled={participationLoading}
                              className="px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 rounded-xl font-bold text-white disabled:opacity-50"
                            >
                              Fight for Attacking Side
                            </button>
                            <button
                              onClick={() => handleJoinWar(false)}
                              disabled={participationLoading}
                              className="px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 rounded-xl font-bold text-white disabled:opacity-50"
                            >
                              Fight for Defending Side
                            </button>
                          </div>
                        </div>
                      );
                    }
                  })()
                ) : (
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <button
                      onClick={() => handleJoinWar(true)}
                      disabled={participationLoading}
                      className="px-6 py-3 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 rounded-xl font-bold text-white disabled:opacity-50"
                    >
                      Fight for Attacking Side
                    </button>
                    <button
                      onClick={() => handleJoinWar(false)}
                      disabled={participationLoading}
                      className="px-6 py-3 bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 rounded-xl font-bold text-white disabled:opacity-50"
                    >
                      Fight for Defending Side
                    </button>
                  </div>
                )}
              </div>
            )}

            {participating && (
              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-6 mb-8">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-green-400 font-bold">You are already participating in this war.</p>
                </div>

                {war.warType === 'revolution' && (
                  <div className="mb-4">
                    <div className="bg-gray-700 p-4 rounded-lg mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 font-medium">Your Current Side:</span>
                        <span className={`font-bold px-3 py-1 rounded-full text-sm ${
                          getUserSideInRevolution() === 'attacker'
                            ? 'bg-gradient-to-r from-red-600 to-red-800 text-white'
                            : 'bg-gradient-to-r from-blue-600 to-blue-800 text-white'
                        }`}>
                          {getUserSideInRevolution() === 'attacker' ? 'Attacker' : 'Defender'}
                        </span>
                      </div>
                    </div>

                    {isWarActive(war.status) && (
                      <div className="text-center">
                        {getUserSideInRevolution() === 'attacker' ? (
                          <button
                            onClick={() => handleSwitchSide('defender')}
                            disabled={participationLoading}
                            className="px-6 py-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {participationLoading ? 'Switching...' : 'Switch to Defender Side'}
                          </button>
                        ) : (
                          <button
                            onClick={() => handleSwitchSide('attacker')}
                            disabled={participationLoading}
                            className="px-6 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {participationLoading ? 'Switching...' : 'Switch to Attacker Side'}
                          </button>
                        )}
                        <p className="text-gray-400 text-xs mt-2">
                          Switching sides requires energy and will immediately change your allegiance
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {isWarActive(war.status) && (
                  <button
                    onClick={() => setActiveTab('participate')}
                    className="w-full px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-500 hover:to-blue-700 rounded-xl font-bold text-white"
                  >
                    Continue Fighting
                  </button>
                )}
              </div>
            )}
          </div>
        )}

        {/* Participate Tab */}
        {activeTab === 'participate' && participating && isWarActive(war.status) && (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">
                {war.warType === 'revolution' ? 'Revolution Participation' : 'War Participation'}
              </h2>
              <p className="text-gray-300 mb-4">
                {war.warType === 'revolution'
                  ? 'Fight for your chosen side in this revolution. Use your energy to deal damage and help your side win.'
                  : 'Use your energy to participate in this war. The more energy you spend, the more damage you\'ll deal to the enemy.'
                }
              </p>
            </div>

            <WarParticipateForm
              warId={id}
              war={war}
              userSide={userCurrentSide}
              onParticipationComplete={handleParticipationComplete}
            />
          </div>
        )}

        {/* Region History Tab */}
        {activeTab === 'history' && war?.defenderRegion?.id && (
          <RegionWarHistory regionId={war.defenderRegion.id} />
        )}

        {/* Regional Performance Tab */}
        {activeTab === 'performance' && war?.defenderRegion?.id && (
          <RegionalPerformance regionId={war.defenderRegion.id} />
        )}

        {/* Participants Modal */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 border-2 border-gray-700 rounded-2xl shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col">
              <div className="flex justify-between items-center p-4 border-b border-gray-700">
                <h2 className="text-xl font-bold text-white">
                  {modalSide === 'attackers' ? 'All Attackers' : 'All Defenders'} ({modalSide === 'attackers' ? consolidatedAttackers.length : consolidatedDefenders.length})
                </h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <CloseIcon className="w-6 h-6" />
                </button>
              </div>

              <div className="p-4 overflow-y-auto flex-grow">
                {modalSide === 'attackers' ? (
                  <ul className="divide-y divide-gray-700">
                    {consolidatedAttackers.map(participant => (
                      <li key={participant.userId} className="py-3 flex justify-between items-center">
                        <div>
                          {participant.userId ? (
                            <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant?.username}
                            </Link>
                          ) : participant.state ? (
                            <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.state.name}
                            </Link>
                          ) : (
                            'Unknown participant'
                          )}
                          {participant.count > 1 && (
                            <span className="ml-2 text-xs text-gray-400">
                              ({participant.count} attacks)
                            </span>
                          )}
                        </div>
                        {participant.totalDamage > 0 && (
                          <span className="bg-red-900 text-red-100 px-2 py-1 rounded text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <ul className="divide-y divide-gray-700">
                    {consolidatedDefenders.map(participant => (
                      <li key={participant.userId} className="py-3 flex justify-between items-center">
                        <div>
                          {participant.userId ? (
                            <Link to={`/users/${participant.userId}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.username}
                            </Link>
                          ) : participant.state ? (
                            <Link to={`/states/${participant.state.id}`} className="text-neonBlue hover:text-blue-400 font-medium">
                              {participant.state.name}
                            </Link>
                          ) : (
                            'Unknown participant'
                          )}
                          {participant.count > 1 && (
                            <span className="ml-2 text-xs text-gray-400">
                              ({participant.count} attacks)
                            </span>
                          )}
                        </div>
                        {participant.totalDamage > 0 && (
                          <span className="bg-blue-900 text-blue-100 px-2 py-1 rounded text-sm">
                            {participant.totalDamage.toLocaleString()} damage
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </div>

              <div className="p-4 border-t border-gray-700">
                <button
                  onClick={() => setShowModal(false)}
                  className="w-full bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white py-2 px-4 rounded-xl"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WarDetail;