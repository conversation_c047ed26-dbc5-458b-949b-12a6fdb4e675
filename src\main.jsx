import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from "react-router-dom";
import App from "./App";
import "./index.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
      <BrowserRouter>
        <App />
        <ToastContainer position="top-right" autoClose={3000} />
      </BrowserRouter>
  </React.StrictMode>
);