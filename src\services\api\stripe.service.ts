import { api } from './api';
import { PremiumPlan } from '../../types/premium';

export const stripeService = {
  // Create a checkout session for gold purchase
  createGoldCheckoutSession: async (
    packageType: string,
    goldAmount?: number,
    successUrl: string = '/payment/success',
    cancelUrl: string = '/payment/cancel',
    currency: string = 'eur'
  ): Promise<{ sessionId: string, url: string }> => {
    // Ensure URLs are complete
    const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5173';

    // If URLs don't start with http or https, prepend the origin
    const fullSuccessUrl = successUrl.startsWith('http') ? successUrl : `${origin}${successUrl}`;
    const fullCancelUrl = cancelUrl.startsWith('http') ? cancelUrl : `${origin}${cancelUrl}`;

    // Get the gold amount based on package type
    let totalGoldAmount = goldAmount;
    if (!totalGoldAmount) {
      // These values should match the ones in ShopPage.jsx
      switch (packageType) {
        case 'small':
          totalGoldAmount = 1000 + 100; // Base + bonus
          break;
        case 'medium':
          totalGoldAmount = 3000 + 500;
          break;
        case 'large':
          totalGoldAmount = 10000 + 2000;
          break;
        case 'extra_large':
          totalGoldAmount = 25000 + 5000;
          break;
      }
    }

    const payload: any = {
      package: packageType,
      currency,
      successUrl: fullSuccessUrl,
      cancelUrl: fullCancelUrl,
      // Include goldAmount for all packages to ensure it's in metadata
      goldAmount: totalGoldAmount,
      // Add payment_intent_data to ensure metadata is passed to the payment intent
      payment_intent_data: {
        metadata: {
          packageType,
          goldAmount: totalGoldAmount
        }
      }
    };

    const response = await api.post('/payments/gold/create-session', payload);
    return response.data;
  },

  // Legacy method - kept for backward compatibility
  createGoldPurchaseIntent: async (amount: number): Promise<{ clientSecret: string }> => {
    const response = await api.post('/payments/gold/create-intent', { amount });
    return response.data;
  },

  // Create a subscription checkout session
  createSubscriptionSession: async (
    plan: PremiumPlan = PremiumPlan.MONTHLY,
    successUrl: string = '/payment/success',
    cancelUrl: string = '/payment/cancel'
  ): Promise<{ sessionId: string, url: string }> => {
    // Ensure URLs are complete
    const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5173';

    // If URLs don't start with http or https, prepend the origin
    const fullSuccessUrl = successUrl.startsWith('http') ? successUrl : `${origin}${successUrl}`;
    const fullCancelUrl = cancelUrl.startsWith('http') ? cancelUrl : `${origin}${cancelUrl}`;

    // Get subscription details based on plan
    let planDetails = {
      name: 'Monthly Premium',
      price: '€2.00',
      period: 'per month'
    };

    switch (plan) {
      case PremiumPlan.SEMIANNUAL:
        planDetails = {
          name: '6-Month Premium',
          price: '€9.00',
          period: 'per 6 months'
        };
        break;
      case PremiumPlan.YEARLY:
        planDetails = {
          name: 'Yearly Premium',
          price: '€12.00',
          period: 'per year'
        };
        break;
    }

    const response = await api.post('/payments/premium/create-session', {
      plan,
      successUrl: fullSuccessUrl,
      cancelUrl: fullCancelUrl,
      // Add metadata to ensure it's passed to the payment intent
      metadata: {
        planType: plan,
        planName: planDetails.name,
        planPrice: planDetails.price,
        planPeriod: planDetails.period
      },
      // Add subscription_data to ensure metadata is passed to the subscription
      subscription_data: {
        metadata: {
          planType: plan,
          planName: planDetails.name
        }
      },
      // Add payment_intent_data to ensure metadata is passed to the payment intent
      payment_intent_data: {
        metadata: {
          planType: plan,
          planName: planDetails.name
        }
      }
    });
    // The API now returns { sessionId, url } instead of { sessionUrl }
    return response.data;
  },

  // Verify premium subscription status
  verifySubscription: async (): Promise<{ isActive: boolean, endsAt: string }> => {
    const response = await api.get('/payments/premium/verify');
    return response.data;
  },

  // Cancel premium subscription
  cancelSubscription: async (): Promise<{ success: boolean, message: string }> => {
    const response = await api.post('/payments/premium/cancel');
    return response.data;
  },

  // Check trial eligibility
  checkTrialEligibility: async (): Promise<{
    eligible: boolean;
    reason?: string;
    canUpgrade?: boolean;
    hasUsedTrial: boolean;
    isPremium: boolean;
    premiumExpiresAt?: string;
  }> => {
    const response = await api.get('/payments/trial-eligibility');
    return response.data;
  },

  // Create a gift premium checkout session
  createGiftPremiumCheckout: async (
    plan: string,
    giftedToUserId: number,
    successUrl?: string,
    cancelUrl: string = '/payment/cancel'
  ): Promise<{ sessionId: string, sessionUrl: string, plan: string, giftedToUserId: number, durationDays: number }> => {
    // Ensure URLs are complete
    const origin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5173';

    let fullSuccessUrl: string | undefined = undefined;
    if (successUrl) {
      fullSuccessUrl = successUrl.startsWith('http') ? successUrl : `${origin}${successUrl}`;
    }
    const fullCancelUrl = cancelUrl.startsWith('http') ? cancelUrl : `${origin}${cancelUrl}`;

    const payload: any = {
      plan,
      giftedToUserId,
      cancelUrl: fullCancelUrl,
    };
    if (fullSuccessUrl) {
      payload.successUrl = fullSuccessUrl;
    }

    const response = await api.post('/payments/premium/gift', payload);
    return response.data;
  },
};