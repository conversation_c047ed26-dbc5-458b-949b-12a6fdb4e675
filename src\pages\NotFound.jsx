import React from 'react';
import { Link } from 'react-router-dom';
import { Home, LogIn, AlertTriangle } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0F0F10] via-[#1C1C1E] to-[#1E2A4A] flex items-center justify-center px-4 relative overflow-hidden">
      {/* Hexagon Background Pattern */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 30 L15 0 L45 0 L60 30 L45 60 L15 60' fill='none' stroke='rgba(63, 135, 255, 0.1)' stroke-width='1'/%3E%3C/svg%3E")`,
            backgroundSize: "60px 60px",
          }}
        ></div>
      </div>

      {/* Targeting Reticle Animation */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 pointer-events-none">
        <div className="absolute inset-0 border-2 border-neonBlue/30 rounded-full animate-ping"></div>
        <div className="absolute inset-0 border border-neonBlue/20 rounded-full animate-pulse"></div>
      </div>

      <div className="relative z-10 w-full max-w-md">
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 shadow-xl font-military">
          {/* Logo */}
          <div className="text-center mb-6">
            <Link to="/" className="flex items-center justify-center mb-4">
              <img
                src="/wn-logo.png"
                alt="Warfront Nations Logo"
                className="h-16 w-auto"
              />
            </Link>
          </div>

          {/* Error Icon */}
          <div className="flex justify-center mb-6">
            <div className="relative">
              <div className="absolute -inset-2 bg-red-500/20 blur-lg"></div>
              <div className="relative bg-red-500/10 border border-red-500/30 rounded-full p-4">
                <AlertTriangle className="h-12 w-12 text-red-400" />
              </div>
            </div>
          </div>

          {/* Error Text */}
          <div className="text-center mb-8">
            <h1 className="text-6xl font-black text-red-400 mb-4 tracking-wider">
              404
            </h1>
            <h2 className="text-2xl font-bold text-white mb-3">
              MISSION ABORTED
            </h2>
            <p className="text-[#A1A1A1] text-sm leading-relaxed">
              The coordinates you're seeking don't exist in our database. 
              The page has either been moved or never existed.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <Link to="/" className="block w-full">
              <button className="w-full group relative px-6 py-3 bg-neonBlue text-white rounded-xl min-w-[200px] overflow-hidden transition-all hover:bg-neonBlue/80">
                <div className="absolute inset-0 bg-gradient-to-r from-neonBlue via-white to-neonBlue opacity-50 blur-xl group-hover:animate-pulse"></div>
                <span className="relative font-bold tracking-wider text-base flex items-center justify-center gap-2">
                  <Home className="h-4 w-4" />
                  RETURN HOME
                </span>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-white/30 transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform"></div>
              </button>
            </Link>
            
            <Link to="/login" className="block w-full">
              <button className="w-full group relative px-6 py-3 bg-transparent text-neonBlue border-2 border-neonBlue rounded-xl min-w-[200px] transition-all hover:bg-neonBlue hover:text-white">
                <span className="relative font-bold tracking-wider text-base flex items-center justify-center gap-2 group-hover:text-white transition-colors">
                  <LogIn className="h-4 w-4" />
                  LOGIN
                </span>
                <div className="absolute inset-0 bg-neonBlue transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform -z-10"></div>
              </button>
            </Link>
          </div>

          {/* Additional Info */}
          <div className="mt-6 text-center">
            <p className="text-xs text-[#777]">
              Error Code: 404 | Status: Page Not Found
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound; 