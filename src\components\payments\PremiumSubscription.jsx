import React, { useState, useEffect } from 'react';
import { stripeService } from '../../services/api/stripe.service';
import { showErrorToast } from '../../utils/showErrorToast';
import { PremiumPlan } from '../../types/premium';

export default function PremiumSubscription() {
  const [loading, setLoading] = useState(false);
  const [trialEligible, setTrialEligible] = useState(true);
  const [trialInfo, setTrialInfo] = useState(null);

  useEffect(() => {
    checkTrialEligibility();
  }, []);

  const checkTrialEligibility = async () => {
    try {
      const data = await stripeService.checkTrialEligibility();
      setTrialEligible(data.eligible);
      setTrialInfo(data);
    } catch (error) {
      console.error('Error checking trial eligibility:', error);
    }
  };

  const handleSubscribe = async () => {
    setLoading(true);
    try {
      // Create success and cancel URLs with query parameters
      const successUrl = `/payment/success?type=subscription&plan=${PremiumPlan.MONTHLY}`;
      const cancelUrl = `/payment/cancel?type=subscription&plan=${PremiumPlan.MONTHLY}`;

      const { url } = await stripeService.createSubscriptionSession(
        PremiumPlan.MONTHLY,
        successUrl,
        cancelUrl
      );
      // Redirect to the Stripe checkout page
      window.location.href = url;
    } catch (error) {
      if (error.message?.includes('already used your free trial')) {
        showErrorToast('You have already used your free trial');
      } else {
        showErrorToast(error);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-800 p-6 rounded-lg">
      <h2 className="text-2xl font-bold text-white mb-4">Premium Subscription</h2>
      
      {!trialEligible && (
        <div className="bg-red-600 text-white p-3 rounded mb-4">
          <strong>⚠️ Trial Already Used</strong>
          <p className="text-sm mt-1">{trialInfo?.reason || 'You have already used your free trial period.'}</p>
        </div>
      )}
      
      {trialEligible && (
        <div className="bg-green-600 text-white p-3 rounded mb-4">
          <strong>🎉 Start Your 2-Week Free Trial!</strong>
          <p className="text-sm mt-1">Cancel anytime.</p>
        </div>
      )}
      
      <div className="space-y-4">
        <div className="text-gray-300">
          <h3 className="text-xl font-semibold text-neonBlue">Trial Benefits:</h3>
          <ul className="list-disc list-inside mt-2">
            <li>Full premium access for 14 days</li>
            <li>50% faster training times</li>
            <li>Auto-mode in wars</li>
            <li>Premium badge</li>
            <li>Additional features...</li>
          </ul>
        </div>
        
        <div className="text-yellow-300 text-sm">
          <p>After your trial ends, you'll be charged $X/month unless you cancel.</p>
        </div>
        
        <button
          onClick={handleSubscribe}
          disabled={loading || !trialEligible}
          className={`w-full py-3 rounded font-medium ${
            trialEligible 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'bg-gray-500 text-gray-300 cursor-not-allowed'
          }`}
        >
          {loading ? 'Processing...' : 
           trialEligible ? 'Start Free Trial' : 'Trial Already Used'}
        </button>
      </div>
    </div>
  );
}