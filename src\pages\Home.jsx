import React, { useEffect, useState } from "react";
import Navbar from "../components/Navbar";
import api from "../services/api/api";
import { useAuthGuard } from "../hooks/useAuthGuard";
import { Link, useNavigate } from "react-router-dom";
import CreateStateModal from "../components/states/CreateStateModal";
import { showErrorToast } from "../utils/showErrorToast";
import GlobalWarStats from "../components/analytics/GlobalWarStats";
import WarTimeline from "../components/analytics/WarTimeline";
import { MapPin, Users, Plane, ArrowRight, Vote, Factory, ArrowDown, ArrowUp } from "lucide-react";
import { stateService } from "../services/api/state.service";
import { warService } from "../services/api/war.service";
import GlobalTravelStatus from "../components/travel/GlobalTravelStatus";
import { travelService } from "../services/api/travel.service";
import SearchableModal from "../components/common/SearchableModal";
import useElectionStore from "../store/useElectionStore";
import ElectionCountdown from "../components/elections/ElectionCountdown";
import Footer from "../components/common/Footer";
import GeneralChatWidget from "../components/chat/GeneralChatWidget";
import NewUserIntro from "../components/intro/NewUserIntro";
import useUserDataStore from "../store/useUserDataStore";

export default function Home() {
  useAuthGuard();
  const navigate = useNavigate();
  const { fetchActiveElection, activeElection, loading: electionLoading } = useElectionStore();
  const { fetchUserData } = useUserDataStore();

  const [stats, setStats] = useState({
    regions: 0,
    population: 0,
    activeWars: 0,
    states: 0,
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [resources, setResources] = useState({
    money: 0,
    energy: 0,
    food: 0,
  });
  const [myState, setMyState] = useState(null);
  const [activeWars, setActiveWars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isCreateStateModalOpen, setIsCreateStateModalOpen] = useState(false);
  const [currentTravel, setCurrentTravel] = useState(null);
  const [travelLoading, setTravelLoading] = useState(false);

  // Modal states
  const [regionsModalOpen, setRegionsModalOpen] = useState(false);
  const [playersModalOpen, setPlayersModalOpen] = useState(false);
  const [regionsData, setRegionsData] = useState([]);
  const [regionsDataCount, setRegionsDataCount] = useState([]);
  const [playersData, setPlayersData] = useState([]);
  const [playersDataCount, setPlayersDataCount] = useState([]);
  const [regionsLoading, setRegionsLoading] = useState(false);
  const [playersLoading, setPlayersLoading] = useState(false);
  
  const [states, setStates] = useState([]);
  const [statesCount, setStatesCount] = useState([]);
  const [statesModalOpen, setStatesModalOpen] = useState(false);
  const [statesLoading, setStatesLoading] = useState(false);

  // Factory-related states
  const [factoriesData, setFactoriesData] = useState([]);
  const [factoriesCount, setFactoriesCount] = useState(0);
  const [factoriesModalOpen, setFactoriesModalOpen] = useState(false);
  const [factoriesLoading, setFactoriesLoading] = useState(false);
  const [factorySort, setFactorySort] = useState({ by: 'NAME', direction: 'ASC' });
  const [factorySearchQuery, setFactorySearchQuery] = useState('');

  const fetchStateData = async () => {
    try {
      const stateResponse = await stateService.getUserState();
      setMyState(stateResponse);
      return stateResponse;
    } catch (error) {
      setMyState(null);
      return null;
    }
  };

  const fetchTravelData = async () => {
    try {
      setTravelLoading(true);
      const travel = await travelService.getCurrentTravel();
      setCurrentTravel(travel);
      return travel;
    } catch (error) {
      setCurrentTravel(null);
      return null;
    } finally {
      setTravelLoading(false);
    }
  };

  const fetchElectionData = async (stateId) => {
    if (!stateId) return null;

    try {
      const election = await fetchActiveElection(stateId);
      return election;
    } catch (error) {
      console.error("Error fetching election data:", error);
      return null;
    }
  };

  const fetchRegionsData = async () => {
    setRegionsLoading(true);
    try {
      const response = await api.get("/regions");
      setRegionsData(response.data);
    } catch (error) {
      console.error("Error fetching regions:", error);
      showErrorToast("Failed to load regions data");
    } finally {
      setRegionsLoading(false);
    }
  };

  const fetchRegionsDataCount = async () => {
    setRegionsLoading(true);
    try {
      const response = await api.get("/regions/count");
      setRegionsDataCount(response.data);
    } catch (error) {
      console.error("Error fetching regions:", error);
      showErrorToast("Failed to load regions data");
    } finally {
      setRegionsLoading(false);
    }
  };

  const fetchPlayersData = async () => {
    setPlayersLoading(true);
    try {
      const response = await api.get("/users");
      setPlayersData(response.data);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setPlayersLoading(false);
    }
  };

  const fetchPlayersDataCount = async () => {
    setPlayersLoading(true);
    try {
      const response = await api.get("/users/count");
      setPlayersDataCount(response.data);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setPlayersLoading(false);
    }
  };

  const fetchStatesData = async () => {
    setStatesLoading(true);
    try {
      const response = await stateService.getAllStates()
      setStates(response);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setStatesLoading(false);
    }
  };

  const fetchStatesDataCount = async () => {
    setStatesLoading(true);
    try {
      const response = await stateService.getAllStatesCount()
      setStatesCount(response);
    } catch (error) {
      console.error("Error fetching players:", error);
      showErrorToast("Failed to load players data");
    } finally {
      setStatesLoading(false);
    }
  };

  const fetchFactoriesData = async () => {
    setFactoriesLoading(true);
    try {
      const response = await api.get("/factories");
      setFactoriesData(response.data);
      setFactoriesCount(response.data.length);
    } catch (error) {
      console.error("Error fetching factories:", error);
      showErrorToast("Failed to load factories data");
    } finally {
      setFactoriesLoading(false);
    }
  };

  const handleItemClick = (item, type) => {
    if (type === "region") {
      navigate(`/regions/${item.id}`);
    } if(type === "player") {
      navigate(`/users/${item.id}`);
    } if(type === "state") {
      navigate(`/states/${item.id}`);
    } if (type === "factory" && item.region?.id) {
      navigate(`/regions/${item.region.id}`);
    }
  };

  const renderRegionItem = (region) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-white">{region.name}</h3>
      </div>
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-400">Population:</span>
          <span className="text-white">
            {region.population?.toLocaleString() || "N/A"}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">State:</span>
          <span className="text-white">
            {region.state?.name || "Independent"}
          </span>
        </div>
        {region.location && (
          <div className="flex justify-between">
            <span className="text-gray-400">Location:</span>
            <span className="text-white">{region.location}</span>
          </div>
        )}
      </div>
    </div>
  );

  const renderPlayerItem = (player) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg text-neonBlue">
            {player?.avatarUrl ? (
              <img
                src={player.avatarUrl}
                alt="Profile Avatar"
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              player?.username?.charAt(0).toUpperCase() || "U"
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">
              {player.username}
            </h3>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Level:</span>
              <span className="text-white font-medium">
                {player.level || 0}
              </span>
            </div>
          </div>
        </div>
        {player.isPremium && (
          <span className="text-yellow-400 text-xs px-2 py-0.5 bg-yellow-900/30 rounded-full">
            PREMIUM
          </span>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Strength:</span>
          <span className="text-white font-medium">{player.strength || 0}</span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Intelligence:</span>
          <span className="text-white font-medium">
            {player.intelligence || 0}
          </span>
        </div>
        <div className="flex justify-between md:flex-col">
          <span className="text-gray-400">Endurance:</span>
          <span className="text-white font-medium">
            {player.endurance || 0}
          </span>
        </div>
      </div>
    </div>
  );

  const renderStateItem = (state) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <div>
            <h3 className="text-lg font-semibold text-white">
              {state.name}
            </h3>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Leader:</span>
              <span className="text-white font-medium">
                {state?.leader?.username || ""}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <span className="text-gray-400">Regions:</span>
              <span className="text-white font-medium">
                {state.regions?.length || 0}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFactoryItem = (factory) => (
    <div>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-white">{factory.name}</h3>
      </div>
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-400">Owner:</span>
          <span className="text-white">
            {factory.owner?.username || "Unknown"}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Region:</span>
          <span className="text-white">
            {factory.region?.name || "Unknown"}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Workers:</span>
          <span className="text-white">
            {factory.workers?.length || factory.factoryWorkers?.length || 0}/{factory.maxWorkers}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Level:</span>
          <span className="text-white">{factory.level}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Experience:</span>
          <span className="text-white">{factory.experience}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-400">Wage:</span>
          <span className="text-white">
            {factory.wage} {factory.wageType === "PERCENTAGE" ? "%" : "units"}
          </span>
        </div>
      </div>
    </div>
  );

  const getSortedFactories = () => {
    let sorted = [...factoriesData];
    
    // Filter by search query
    if (factorySearchQuery) {
      const query = factorySearchQuery.toLowerCase();
      sorted = sorted.filter(factory => 
        factory.name.toLowerCase().includes(query) || 
        (factory.region?.name && factory.region.name.toLowerCase().includes(query))
      );
    }
    
    // Apply sorting
    if (factorySort.by === 'LEVEL') {
      sorted.sort((a, b) => 
        factorySort.direction === 'DESC' ? b.level - a.level : a.level - b.level
      );
    } else if (factorySort.by === 'EXPERIENCE') {
      sorted.sort((a, b) => 
        factorySort.direction === 'DESC' ? b.experience - a.experience : a.experience - b.experience
      );
    } else if (factorySort.by === 'NAME') {
      sorted.sort((a, b) => 
        factorySort.direction === 'DESC' 
          ? b.name.localeCompare(a.name) 
          : a.name.localeCompare(b.name)
      );
    }
    
    return sorted;
  };

  const toggleSort = (sortBy) => {
    if (factorySort.by === sortBy) {
      // Toggle direction if same column clicked
      const newDirection = factorySort.direction === 'DESC' ? 'ASC' : 'DESC';
      setFactorySort({
        by: sortBy,
        direction: newDirection
      });
    } else {
      // Set new column with default DESC direction
      setFactorySort({
        by: sortBy,
        direction: 'DESC'
      });
    }
  };

  const getSortIcon = (sortBy) => {
    if (factorySort.by !== sortBy) return null;
    return factorySort.direction === 'DESC' 
      ? <ArrowDown className="w-3 h-3 ml-1" /> 
      : <ArrowUp className="w-3 h-3 ml-1" />;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch user data for the intro system
        await fetchUserData();

        const stateData = await fetchStateData();
        await fetchTravelData();
        await fetchStatesDataCount();
        await fetchRegionsDataCount();
        await fetchPlayersDataCount();
        await fetchFactoriesData();

        if (stateData && stateData.id) {
          await fetchElectionData(stateData.id);
        }

        let wars = [];
        try {
          wars = await warService.getActiveWars();
          setActiveWars(wars);
        } catch (error) {
          console.error("Error fetching wars:", error);
        }

        setStats({
          regions: 0,
          population: 0,
          activeWars: wars.length,
          states: states.length,
        });
      } catch (error) {
        console.error("Failed to fetch data:", error);
        showErrorToast("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [fetchUserData]);

  const refreshStateData = async () => {
    try {
      const state = await fetchStateData();
      return state;
    } catch (error) {
      console.error("Failed to refresh state data:", error);
      return null;
    }
  };

  const onViewRegions = () => {
    setRegionsModalOpen(true);
    if(regionsData.length === 0){
      fetchRegionsData();
    }
  };

  const onViewPlayers = () => {
    setPlayersModalOpen(true);
    if(playersData.length === 0){
      fetchPlayersData();
    }
  };

  const onViewWars = () => {
    navigate("/wars");
  };
  const onViewStates = () => {
    setStatesModalOpen(true);
    if(states.length === 0){
      fetchStatesData();
    }
  };

  const onViewFactories = () => {
    setFactoriesModalOpen(true);
    if(factoriesData.length === 0){
      fetchFactoriesData();
    }
  };

  const closeRegionsModal = () => {
    setRegionsModalOpen(false);
    setRegionsData([]);
  };

  const closePlayersModal = () => {
    setPlayersModalOpen(false);
    setPlayersData([]);
  };

  const closeStatesModal = () => {
    setStatesModalOpen(false);
  };

  const closeFactoriesModal = () => {
    setFactoriesModalOpen(false);
    setFactorySearchQuery('');
    setFactorySort({ by: 'NAME', direction: 'ASC' });
  };

  const getStatusColor = (status) => {
    if (!status) return "text-gray-400";
    switch (status.toLowerCase()) {
      case "controlled":
        return "text-green-400";
      case "contested":
        return "text-yellow-400";
      case "expanding":
        return "text-blue-400";
      case "active":
        return "text-green-400";
      case "online":
        return "text-emerald-400";
      case "offline":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading...</div>
        </div>
      </div>
    );
  }

  // Prepare sorted factories
  const sortedFactories = getSortedFactories();

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Travel Status Section */}
        {currentTravel && currentTravel.status === "in_progress" && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Plane className="w-5 h-5 text-blue-400 mr-2" />
                Travel Status
              </h2>
              <Link
                to="/travel/permissions"
                className="text-blue-400 hover:text-blue-300 text-sm flex items-center"
              >
                Manage Travel <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <GlobalTravelStatus />
          </div>
        )}

        {/* Election Status Section */}
        {activeElection && activeElection.status === "active" && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Vote className="w-5 h-5 text-purple-400 mr-2" />
                Active Election
              </h2>
              <Link
                to={`/states/${activeElection.state?.id}?tab=elections`}
                className="text-purple-400 hover:text-purple-300 text-sm flex items-center"
              >
                View Election <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 mb-1">
                    Election in progress for <span className="text-white font-medium">{activeElection.state?.name}</span>
                  </p>
                  <p className="text-sm text-gray-400">
                    {activeElection.candidates?.length || 0} candidates • {activeElection.totalVotes || 0} votes cast
                  </p>
                </div>
              </div>
              {activeElection.endTime && (
                <ElectionCountdown endTime={activeElection.endTime} />
              )}
            </div>
          </div>
        )}

        {/* Travel Quick Access */}
        {(!currentTravel || currentTravel.status !== "in_progress") && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6" data-tour="travel-section">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Plane className="w-5 h-5 text-blue-400 mr-2" />
                Travel
              </h2>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 mb-2">Plan your next journey</p>
                <p className="text-sm text-gray-400">
                  Travel to different regions to explore new opportunities
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6" data-tour="stats-grid">
            <div className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
           onClick={onViewStates}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">States</h3>
              <span className="text-neonBlue text-2xl">🗺️</span>
            </div>
            <p className="text-3xl font-bold text-white">
              {statesCount || 0}
            </p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all states
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewRegions}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Regions</h3>
              <span className="text-neonBlue text-2xl">🌍</span>
            </div>
            <p className="text-3xl font-bold text-white">{regionsDataCount || 0}</p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all regions
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewPlayers}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Total Population</h3>
              <span className="text-neonBlue text-2xl">👥</span>
            </div>
            <p className="text-3xl font-bold text-white">
              {playersDataCount || 0}
            </p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all players
            </p>
          </div>

          <div
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewWars}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Active Wars</h3>
              <span className="text-neonBlue text-2xl">⚔️</span>
            </div>
            <p className="text-3xl font-bold text-white">{stats.activeWars}</p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all wars
            </p>
          </div>

          {/* Factories Card */}
          <div 
            className="bg-gray-800 rounded-lg shadow-lg p-6 cursor-pointer hover:bg-gray-700 transition-all duration-200 hover:shadow-xl hover:scale-105"
            onClick={onViewFactories}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-gray-400">Factories</h3>
              <span className="text-neonBlue text-2xl">🏭</span>
            </div>
            <p className="text-3xl font-bold text-white">{factoriesCount}</p>
            <p className="text-sm text-neonBlue mt-2 opacity-75">
              Click to view all factories
            </p>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="mb-8" data-tour="global-stats">
          <GlobalWarStats />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              Recent Activity
            </h2>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === "conquest"
                          ? "bg-neonBlue"
                          : activity.type === "resource"
                          ? "bg-yellow-400"
                          : "bg-red-400"
                      }`}
                    ></div>
                    <div>
                      <p className="text-white">{activity.description}</p>
                      <p className="text-sm text-gray-400">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-400">No recent activity to display.</p>
              )}
            </div>
          </div>

          {/* State Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6" data-tour="my-state">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">My State</h2>
              {myState && (
                <Link
                  to={`/states/${myState.id}`}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  View Details
                </Link>
              )}
            </div>

            {myState ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center text-xl text-neonBlue">
                    {myState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-white">
                      {myState.name}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      Leader: {myState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Regions:</span>
                    <span className="text-white">
                      {myState.regions?.length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Treasury:</span>
                    <span className="text-white">
                      {myState.treasury?.toLocaleString() || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span
                      className={`${
                        myState.isActive ? "text-green-400" : "text-red-400"
                      }`}
                    >
                      {myState.isActive ? "Active" : "Inactive"}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">
                  You don't belong to any state yet.
                </p>
                <button
                  onClick={() => setIsCreateStateModalOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded inline-block"
                >
                  Create a State
                </button>
              </div>
            )}
          </div>

          {/* Active Wars */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6" data-tour="active-wars">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">Active Wars</h2>
              <Link
                to="/wars"
                className="text-red-400 hover:text-red-300 text-sm"
              >
                View All Wars
              </Link>
            </div>

            {activeWars.length > 0 ? (
              <div className="max-h-80 overflow-y-auto custom-scrollbar">
                <div className="space-y-4 pr-2">
                  {activeWars.slice(0, 5).map((war) => (
                    <div
                      key={war.id}
                      className="border-b border-gray-700 pb-4 last:border-0 last:pb-0"
                    >
                      <h3 className="text-lg font-medium text-white mb-2">
                        {war.warType} War
                      </h3>
                      <div className="space-y-1 mb-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Attacker:</span>
                          <p className="text-gray-300 mb-1">
                            <Link
                              to={`/regions/${war.attackerRegion.id}`}
                              className="text-neonBlue hover:text-blue-400"
                            >
                              {war.attackerRegion.name}
                            </Link>
                          </p>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Defender:</span>
                          <p className="text-gray-300 mb-1">
                            <Link
                              to={`/regions/${war.defenderRegion.id}`}
                              className="text-neonBlue hover:text-blue-400"
                            >
                              {war.defenderRegion.name}
                            </Link>
                          </p>
                        </div>
                      </div>
                      <Link
                        to={`/wars/${war.id}`}
                        className="text-red-400 hover:text-red-300 text-sm"
                      >
                        View War Details
                      </Link>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400 mb-4">No active wars.</p>
                <Link
                  to="/wars/new"
                  className="bg-red-600 hover:bg-red-700 !text-white px-4 py-2 rounded inline-block [&]:text-white [&:hover]:text-white"
                >
                  Declare War
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* War Timeline */}
        <div className="mt-6 bg-gray-800 rounded-lg shadow-lg p-6">
          <WarTimeline limit={5} />
        </div>
      </div>

      {/* Regions Modal */}
      <SearchableModal
        isOpen={regionsModalOpen}
        onClose={closeRegionsModal}
        title="All Regions"
        icon={MapPin}
        data={regionsData}
        loading={regionsLoading}
        onItemClick={(item) => handleItemClick(item, "region")}
        renderItem={renderRegionItem}
        searchPlaceholder="Search regions by name"
      />

      {/* Players Modal */}
      <SearchableModal
        isOpen={playersModalOpen}
        onClose={closePlayersModal}
        title="All Players"
        icon={Users}
        data={playersData}
        loading={playersLoading}
        onItemClick={(item) => handleItemClick(item, "player")}
        renderItem={renderPlayerItem}
        searchPlaceholder="Search players by username"
      />
      
      {/* States Modal */}
      <SearchableModal
        isOpen={statesModalOpen}
        onClose={closeStatesModal}
        title="All States"
        icon={Users}
        data={states}
        loading={statesLoading}
        onItemClick={(item) => handleItemClick(item, "state")}
        renderItem={renderStateItem}
        searchPlaceholder="Search players by state name"
      />

      {/* Factories Modal */}
      {factoriesModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
            {/* Fixed Header */}
            <div className="sticky top-0 bg-gray-800 z-10 p-6 border-b border-gray-700 shadow-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-white flex items-center">
                  <Factory className="w-6 h-6 text-blue-400 mr-2" />
                  All Factories
                </h2>
                <button 
                  onClick={closeFactoriesModal} 
                  className="text-gray-400 hover:text-white text-3xl"
                >
                  &times;
                </button>
              </div>
              
              {/* Search Bar */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search factories by name or region"
                  className="w-full bg-gray-750 text-white py-3 pl-10 pr-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={factorySearchQuery}
                  onChange={(e) => setFactorySearchQuery(e.target.value)}
                />
                <div className="absolute left-3 top-3 text-gray-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* Sorting Buttons */}
              <div className="flex flex-wrap gap-2 mb-4">
                <button
                  onClick={() => toggleSort('NAME')}
                  className={`px-3 py-2 rounded-lg text-sm font-medium flex items-center ${
                    factorySort.by === 'NAME'
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <span>Name</span>
                  {getSortIcon('NAME')}
                </button>
                
                <button
                  onClick={() => toggleSort('LEVEL')}
                  className={`px-3 py-2 rounded-lg text-sm font-medium flex items-center ${
                    factorySort.by === 'LEVEL'
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <span>Level</span>
                  {getSortIcon('LEVEL')}
                </button>
                
                <button
                  onClick={() => toggleSort('EXPERIENCE')}
                  className={`px-3 py-2 rounded-lg text-sm font-medium flex items-center ${
                    factorySort.by === 'EXPERIENCE'
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  <span>Experience</span>
                  {getSortIcon('EXPERIENCE')}
                </button>
              </div>

              {/* Factory Count */}
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-400">
                  {factoriesData.length} factories total
                </span>
                <span className="text-gray-400">
                  {sortedFactories.length} displayed
                </span>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-grow overflow-auto">
              {factoriesLoading ? (
                <div className="text-center py-8">
                  <div className="text-neonBlue">Loading factories...</div>
                </div>
              ) : sortedFactories.length > 0 ? (
                <div className="space-y-4 p-6 pt-0">
                  {sortedFactories.map((factory) => (
                    <div
                      key={factory.id}
                      className="p-4 bg-gray-750 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors"
                      onClick={() => handleItemClick(factory, "factory")}
                    >
                      {renderFactoryItem(factory)}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400">No factories found</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create State Modal */}
      <CreateStateModal
        isOpen={isCreateStateModalOpen}
        onClose={() => setIsCreateStateModalOpen(false)}
        onSuccess={(newState) => {
          if (newState) {
            setMyState(newState);
          } else {
            refreshStateData();
          }
          setIsCreateStateModalOpen(false);
        }}
      />

      <Footer />

      {/* General Chat Widget */}
      <div data-tour="chat-widget">
        <GeneralChatWidget />
      </div>

      {/* New User Intro */}
      <NewUserIntro />
    </div>
  );
}