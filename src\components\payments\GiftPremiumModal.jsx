import React, { useState } from 'react';
import { FaCrown, FaGift, FaSpinner, FaTimes } from 'react-icons/fa';
import { stripeService } from '../../services/api/stripe.service';
import { showErrorToast } from '../../utils/showErrorToast';
import { showSuccessToast } from '../../utils/showSuccessToast';

const GiftPremiumModal = ({ isOpen, onClose, recipientUser }) => {
  const [selectedPlan, setSelectedPlan] = useState('monthly');
  const [isLoading, setIsLoading] = useState(false);

  const plans = [
    {
      id: 'monthly',
      name: '30 Days Premium',
      duration: '30 days',
      price: '€2.00',
      description: 'Perfect for a short-term boost'
    },
    {
      id: 'semiannual',
      name: '180 Days Premium',
      duration: '180 days',
      price: '€9.00',
      description: 'Great value for extended benefits'
    },
    {
      id: 'yearly',
      name: '365 Days Premium',
      duration: '365 days',
      price: '€12.00',
      description: 'Best value for long-term players'
    }
  ];

  const handleGiftPremium = async () => {
    if (!recipientUser) {
      showErrorToast('No recipient selected');
      return;
    }

    setIsLoading(true);
    try {
      const result = await stripeService.createGiftPremiumCheckout(
        selectedPlan,
        recipientUser.id,
        undefined, // Let backend use its default with all info
        '/payment/cancel'
      );

      // Redirect to Stripe checkout
      if (result.sessionUrl) {
        window.location.href = result.sessionUrl;
      } else {
        showErrorToast('Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating gift premium checkout:', error);
      showErrorToast(error.response?.data?.message || 'Failed to create gift premium checkout');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl max-w-sm w-full p-4 border border-gray-700/50 mx-2">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white flex items-center">
            <FaGift className="mr-2 text-yellow-400" />
            Gift Premium
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-300 transition-colors"
            disabled={isLoading}
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Recipient Info */}
        {recipientUser && (
          <div className="mb-4 p-2 bg-blue-600/10 border border-blue-500/30 rounded-xl">
            <p className="text-sm text-gray-400 mb-1">Gifting to:</p>
            <p className="font-semibold text-white text-lg">{recipientUser.username}</p>
          </div>
        )}

        {/* Plan Selection */}
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Premium Plan
          </label>
          <div className="space-y-2">
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`border rounded-xl p-2 cursor-pointer transition-all ${
                  selectedPlan === plan.id
                    ? 'border-yellow-500 bg-yellow-600/10 shadow-lg shadow-yellow-500/20'
                    : 'border-gray-600 hover:border-gray-500 bg-gray-700/30 hover:bg-gray-700/50'
                }`}
                onClick={() => setSelectedPlan(plan.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <FaCrown className={`mr-2 ${selectedPlan === plan.id ? 'text-yellow-400' : 'text-gray-400'}`} />
                      <h3 className={`font-semibold ${selectedPlan === plan.id ? 'text-yellow-400' : 'text-white'}`}>
                        {plan.name}
                      </h3>
                    </div>
                    <p className="text-sm text-gray-400 mb-1">{plan.description}</p>
                    <p className="text-xs text-gray-500">Duration: {plan.duration}</p>
                  </div>
                  <div className="text-right ml-4">
                    <p className={`font-bold text-lg ${selectedPlan === plan.id ? 'text-yellow-400' : 'text-white'}`}>
                      {plan.price}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-xl transition-colors font-medium"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleGiftPremium}
            disabled={isLoading || !recipientUser}
            className={`flex-1 px-4 py-2 bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 text-white rounded-xl flex items-center justify-center transition-all shadow hover:shadow-lg hover:shadow-yellow-500/20 font-medium ${
              isLoading || !recipientUser ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isLoading ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                <FaGift className="mr-2" />
                Gift Premium
              </>
            )}
          </button>
        </div>

        {/* Info Text */}
        <div className="mt-4 p-2 bg-gray-700/30 rounded-xl border border-gray-600/50">
          <div className="space-y-2 text-xs text-gray-400">
            <p className="flex items-center">
              <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></span>
              Premium will be applied immediately after payment
            </p>
            <p className="flex items-center">
              <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2"></span>
              Premium duration stacks with existing premium
            </p>
            <p className="flex items-center">
              <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2"></span>
              You cannot gift premium to yourself
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GiftPremiumModal; 