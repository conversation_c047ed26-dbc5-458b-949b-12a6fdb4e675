/* Custom animations for the New User Intro */

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 20px rgba(100, 108, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 30px rgba(100, 108, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 20px rgba(100, 108, 255, 0.6);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom styles for joyride elements */
.warfront-intro-tooltip {
  animation: fadeInUp 0.3s ease-out;
}

.warfront-intro-beacon {
  animation: pulse 2s infinite, glow 2s infinite;
}

/* Hover effects for buttons */
.warfront-intro-button-next:hover {
  background-color: #535bf2 !important;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(100, 108, 255, 0.4) !important;
}

.warfront-intro-button-back:hover {
  background-color: #4b5563 !important;
  transform: translateY(-1px);
}

.warfront-intro-button-skip:hover {
  border-color: #9ca3af !important;
  color: #ffffff !important;
  background-color: rgba(156, 163, 175, 0.1) !important;
}

/* Custom spotlight effect */
.warfront-intro-spotlight {
  animation: glow 2s infinite;
}

/* Progress indicator styling */
.warfront-intro-progress {
  background: linear-gradient(90deg, #646cff 0%, #535bf2 100%);
  border-radius: 10px;
  height: 4px;
}

/* Custom overlay styling */
.warfront-intro-overlay {
  background: radial-gradient(circle at center, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.9) 100%);
  backdrop-filter: blur(2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .warfront-intro-tooltip {
    max-width: 90vw !important;
  }
  
  .warfront-intro-tooltip .tooltip-container {
    padding: 16px !important;
  }
  
  .warfront-intro-button-next,
  .warfront-intro-button-back,
  .warfront-intro-button-skip {
    padding: 8px 16px !important;
    font-size: 13px !important;
  }
}

/* Special styling for welcome and completion steps */
.warfront-intro-welcome,
.warfront-intro-completion {
  text-align: center;
}

.warfront-intro-welcome h2,
.warfront-intro-completion h2 {
  background: linear-gradient(135deg, #646cff 0%, #535bf2 50%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.warfront-intro-tips-box {
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);
  border: 1px solid rgba(100, 108, 255, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
}

.warfront-intro-tips-box h4 {
  color: #646cff;
  margin-bottom: 12px;
  font-weight: 600;
}

.warfront-intro-tips-box ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.warfront-intro-tips-box li {
  padding: 4px 0;
  color: #d1d5db;
  position: relative;
  padding-left: 20px;
}

.warfront-intro-tips-box li::before {
  content: "•";
  color: #646cff;
  font-weight: bold;
  position: absolute;
  left: 0;
}
