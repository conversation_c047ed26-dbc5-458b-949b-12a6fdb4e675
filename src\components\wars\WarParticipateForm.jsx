import React, { useState } from "react";
import { warService } from "../../services/api/war.service";
import useUserDataStore from "../../store/useUserDataStore";
import { showSuccessToast } from "../../utils/showSuccessToast";
import { showErrorToast } from "../../utils/showErrorToast";
import EnergyIcon from "../svg/EnergyIcon";
import CheckmarkIcon from "../svg/CheckmarkIcon";
import SpinnerIcon from "../svg/SpinnerIcon";

const WarParticipateForm = ({ warId, war, userSide, onParticipationComplete }) => {
  const { userData, fetchUserData } = useUserDataStore();
  const [loading, setLoading] = useState(false);
  const isPremium = userData?.isPremium || false;
  const currentEnergy = userData?.energy || 0;
  const isAutoModeActive = userData?.activeAutoMode === 'war';
  
  const handleManualAttack = async () => {
    setLoading(true);
    try {
      await fetchUserData(true);
      const latestEnergy = userData?.energy || 0;
      if (latestEnergy <= 0) {
        showErrorToast("You do not have enough energy to participate in the war.");
        return;
      }
      const participateData = {
        energyAmount: latestEnergy,
        autoMode: false,
      };
      if (war?.warType === 'revolution' && userSide) {
        participateData.side = userSide;
      }
      const result = await warService.participateInWar(warId, participateData);
      showSuccessToast("Successfully participated in war!");
      await fetchUserData(true);
      if (onParticipationComplete) onParticipationComplete(result);
    } catch (error) {
      console.error("Failed to participate in war:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleEnableAutoMode = async () => {
    setLoading(true);
    try {
      await fetchUserData(true);
      const latestEnergy = userData?.energy || 0;
      if (latestEnergy <= 0) {
        showErrorToast("You do not have enough energy to participate in the war.");
        return;
      }
      const participateData = {
        energyAmount: latestEnergy,
        autoMode: true,
        autoEnergyPercentage: 100,
      };
      if (war?.warType === 'revolution' && userSide) {
        participateData.side = userSide;
      }
      const result = await warService.participateInWar(warId, participateData);
      showSuccessToast("Auto mode enabled! The system will attack every 30 minutes.");
      await fetchUserData(true);
      if (onParticipationComplete) onParticipationComplete(result);
    } catch (error) {
      console.error("Failed to enable auto mode:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleDisableAutoMode = async () => {
    setLoading(true);
    try {
      const result = await warService.stopAutoAttack(warId);
      showSuccessToast(result.message || "Auto mode disabled successfully!");
      await fetchUserData(true);
      if (onParticipationComplete) onParticipationComplete();
    } catch (error) {
      console.error("Failed to disable auto mode:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSwitchSide = async (newSide) => {
    if (war?.warType !== 'revolution' || newSide === userSide) return;
    setLoading(true);
    try {
      await fetchUserData(true);
      const latestEnergy = userData?.energy || 0;
      if (latestEnergy <= 0) {
        showErrorToast("You need energy to switch sides.");
        return;
      }
      const participateData = {
        energyAmount: latestEnergy,
        autoMode: false,
        side: newSide
      };
      await warService.participateInWar(warId, participateData);
      showSuccessToast(`Successfully switched to ${newSide} side!`);
      await fetchUserData(true);
      if (onParticipationComplete) onParticipationComplete();
    } catch (error) {
      console.error("Failed to switch sides:", error);
      showErrorToast(error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-lg p-6">
      <h2 className="text-2xl font-bold text-white mb-6 text-center bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
        WAR PARTICIPATION
      </h2>

      {/* Revolution Side Display and Switch */}
      {war?.warType === 'revolution' && userSide && (
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5 mb-6">
          <div className="flex justify-between items-center mb-4">
            <span className="text-gray-300 font-medium">YOUR SIDE:</span>
            <span className={`font-bold px-3 py-1 rounded-full text-sm ${
              userSide === 'attacker' 
                ? 'bg-gradient-to-r from-red-600 to-red-800 text-white' 
                : 'bg-gradient-to-r from-blue-600 to-blue-800 text-white'
            }`}>
              {userSide === 'attacker' ? 'ATTACKER' : 'DEFENDER'}
            </span>
          </div>
          <div className="text-center">
            {userSide === 'attacker' ? (
              <button
                onClick={() => handleSwitchSide('defender')}
                disabled={loading || currentEnergy <= 0}
                className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
                  loading || currentEnergy <= 0
                    ? 'bg-gray-700 cursor-not-allowed text-gray-500'
                    : 'bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-500 hover:to-blue-700 text-white'
                } border-2 border-blue-700 shadow-lg shadow-blue-900/30`}
              >
                {loading ? 'SWITCHING...' : 'SWITCH TO DEFENDER SIDE'}
              </button>
            ) : (
              <button
                onClick={() => handleSwitchSide('attacker')}
                disabled={loading || currentEnergy <= 0}
                className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
                  loading || currentEnergy <= 0
                    ? 'bg-gray-700 cursor-not-allowed text-gray-500'
                    : 'bg-gradient-to-r from-red-600 to-red-800 hover:from-red-500 hover:to-red-700 text-white'
                } border-2 border-red-700 shadow-lg shadow-red-900/30`}
              >
                {loading ? 'SWITCHING...' : 'SWITCH TO ATTACKER SIDE'}
              </button>
            )}
            <p className="text-gray-400 text-xs mt-2">
              Switching sides requires energy and will immediately change your allegiance
            </p>
          </div>
        </div>
      )}

      {/* Energy Display */}
      <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5 mb-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <EnergyIcon />
            <span className="text-gray-300 font-medium">YOUR ENERGY:</span>
          </div>
          <span className={`text-xl font-bold ${currentEnergy > 0 ? "text-neonBlue" : "text-red-400"}`}>
            {currentEnergy}
          </span>
        </div>
      </div>

      <div className="space-y-6">
        {/* Manual Attack Button */}
        <div>
          <h3 className="text-lg font-bold text-white mb-3">MANUAL ATTACK</h3>
          <p className="text-gray-300 mb-4">
            Attack now using all your available energy.
          </p>
          <button
            onClick={handleManualAttack}
            disabled={loading || currentEnergy <= 0}
            className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
              loading || currentEnergy <= 0
                ? 'bg-gray-700 cursor-not-allowed text-gray-500'
                : 'bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white'
            } border-2 border-red-800 shadow-lg shadow-red-900/30`}
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <SpinnerIcon />
                PROCESSING...
              </span>
            ) : currentEnergy <= 0 ? (
              "NO ENERGY"
            ) : (
              "ATTACK NOW"
            )}
          </button>
        </div>

        {/* Auto Mode Section */}
        <div className="border-t border-gray-700 pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-white">AUTO MODE</h3>
            {!isPremium && (
              <span className="bg-gradient-to-r from-yellow-600 to-yellow-800 text-yellow-100 text-xs px-2 py-1 rounded-full">
                PREMIUM ONLY
              </span>
            )}
          </div>

          <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 p-4 rounded-xl text-sm text-gray-300 mb-4">
            <p className="mb-3">
              When auto mode is enabled, the system will automatically attack
              every 30 minutes using all your available energy.
            </p>
            {!isPremium && (
              <p className="text-yellow-400 mt-2">
                This feature requires a premium account. Upgrade to premium to
                enable auto mode.
              </p>
            )}
            {isPremium && isAutoModeActive && (
              <div className="mt-3 p-3 bg-green-900/30 rounded-lg border border-green-700">
                <div className="flex items-center">
                  <CheckmarkIcon />
                  <p className="text-green-400 font-medium">
                    AUTO MODE IS ACTIVE FOR THIS WAR
                  </p>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-3">
            {/* Enable Auto Mode Button */}
            {isPremium && !isAutoModeActive && (
              <button
                onClick={handleEnableAutoMode}
                disabled={loading || currentEnergy <= 0}
                className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
                  loading || currentEnergy <= 0
                    ? 'bg-gray-700 cursor-not-allowed text-gray-500'
                    : 'bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 text-white'
                } border-2 border-blue-800 shadow-lg shadow-blue-900/30`}
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <SpinnerIcon />
                    PROCESSING...
                  </span>
                ) : currentEnergy <= 0 ? (
                  "NO ENERGY"
                ) : (
                  "ENABLE AUTO MODE"
                )}
              </button>
            )}

            {/* Disable Auto Mode Button */}
            {isPremium && isAutoModeActive && (
              <button
                onClick={handleDisableAutoMode}
                disabled={loading}
                className={`w-full py-3 px-4 rounded-xl font-bold transition-all ${
                  loading
                    ? 'bg-gray-700 cursor-not-allowed text-gray-500'
                    : 'bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white'
                } border-2 border-red-800 shadow-lg shadow-red-900/30`}
              >
                {loading ? "PROCESSING..." : "DISABLE AUTO MODE"}
              </button>
            )}

            {/* Show message when user doesn't have premium */}
            {!isPremium && (
              <div className="w-full bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 py-3 px-4 rounded-xl text-gray-400 text-center font-medium">
                PREMIUM REQUIRED FOR AUTO MODE
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarParticipateForm;