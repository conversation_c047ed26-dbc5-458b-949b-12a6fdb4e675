import React, { useState, useEffect, useRef } from 'react';
import { GeneralChatService } from '../../services/api/general-chat.service';
import { showErrorToast } from '../../utils/showErrorToast';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { formatDate } from '../../utils/formatDate';
import useAuthStore from '../../store/useAuthStore';
import useChatStore from '../../store/useChatStore';

const GeneralChatWidget = () => {
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const { user } = useAuthStore();
  const { 
    generalChatUnreadCount, 
    setGeneralChatUnreadCount,
    sendGeneralChatMessage,
    markGeneralChatAsRead,
    fetchGeneralChatMessages,
    messages,
    messagesLoading,
    messagesHasMore,
    isConnected,
    connect
  } = useChatStore();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    } else if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      });
    }
  };

  useEffect(() => {
    const initializeChat = async () => {
      try {
        // Initialize WebSocket connection
        await connect().catch(error => {
          console.warn('Failed to connect to WebSocket, using HTTP fallback:', error);
        });
        
        // Load initial data
        await Promise.all([
          loadGeneralChatInfo(),
          loadGeneralChatUnreadCount(),
          fetchGeneralChatMessages()
        ]);
      } catch (error) {
        console.error('Failed to initialize chat:', error);
      }
    };

    initializeChat();
  }, []);

  // Refresh unread count periodically when not connected
  useEffect(() => {
    if (!isConnected) {
      const interval = setInterval(() => {
        loadGeneralChatUnreadCount();
      }, 10000); // Refresh every 10 seconds when not connected

      return () => clearInterval(interval);
    }
  }, [isConnected]);



  // Load messages if not available in store
  useEffect(() => {
    if (isConnected && (!messages['general'] || messages['general'].length === 0)) {
      fetchGeneralChatMessages().then(() => {
        // Scroll to bottom after messages are loaded
        setTimeout(() => {
          scrollToBottom();
        }, 100);
      });
    }
  }, [isConnected, messages['general'], fetchGeneralChatMessages]);

  // Scroll to bottom when messages change or chat is expanded
  useEffect(() => {
    if (isExpanded) {
      // Use setTimeout to ensure DOM is rendered
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [messages, isExpanded]);

  const loadGeneralChatInfo = async () => {
    try {
      const info = await GeneralChatService.getGeneralChatInfo();
      setGeneralChatUnreadCount(info.unreadCount);
    } catch (error) {
      console.error('Failed to load general chat info:', error);
    }
  };

  const loadGeneralChatUnreadCount = async () => {
    try {
      const response = await GeneralChatService.getGeneralChatUnreadCountByUser();
      setGeneralChatUnreadCount(response.unreadCount);
    } catch (error) {
      console.error('Failed to load general chat unread count:', error);
    }
  };

  const loadMessages = async (cursor = null) => {
    if (messagesLoading['general']) return;
    
    try {
      await fetchGeneralChatMessages(cursor);
    } catch (error) {
      showErrorToast('Failed to load messages');
      console.error('Failed to load messages:', error);
    }
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || sending) return;

    setSending(true);
    try {
      await sendGeneralChatMessage(newMessage.trim());
      setNewMessage('');
      // Scroll to bottom after sending message
      setTimeout(() => {
        scrollToBottom();
      }, 100);
      // showSuccessToast('Message sent!');
    } catch (error) {
      showErrorToast('Failed to send message');
      console.error('Failed to send message:', error);
    } finally {
      setSending(false);
    }
  };

  const markAsRead = async () => {
    try {
      await markGeneralChatAsRead();
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      markAsRead();
      // Scroll to bottom when expanding
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    } else {
      // Also scroll when collapsing to ensure proper positioning
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
  };

  const loadMoreMessages = () => {
    if (messages['general'] && messages['general'].length > 0 && messagesHasMore['general']) {
      const oldestMessage = messages['general'][0];
      loadMessages(oldestMessage.id);
    }
  };

  // Collapsed state - floating button
  if (!isExpanded) {
    return (
      <div className={`fixed z-50 ${isMobile ? 'bottom-20 right-4' : 'bottom-6 right-6'}`}>
        <button
          onClick={handleExpand}
          className="bg-gray-800 hover:bg-gray-700 text-white rounded-full p-4 shadow-lg border border-gray-600 transition-all duration-200 hover:scale-105 flex items-center space-x-2 group"
        >
          <svg className="w-6 h-6 text-neonBlue group-hover:text-blue-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          {generalChatUnreadCount > 0 && (
            <span className="bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold animate-pulse">
              {generalChatUnreadCount > 99 ? '99+' : generalChatUnreadCount}
            </span>
          )}
        </button>
      </div>
    );
  }

  // Expanded state - chat window
  return (
    <div className={`fixed z-50 ${isMobile ? 'bottom-20 right-4 w-[calc(100vw-2rem)] h-[calc(100vh-8rem-4rem)]' : 'bottom-6 right-6 w-96 h-[500px]'} bg-gray-800 rounded-lg shadow-2xl border border-gray-600 flex flex-col`}>
      {/* Header */}
      <div className="bg-gray-700 text-white p-4 rounded-t-lg flex justify-between items-center border-b border-gray-600">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-neonBlue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <div>
            <h3 className="font-semibold text-white">General Chat</h3>
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
              <span className="text-xs text-gray-300">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>
          </div>
        </div>
        <button
          onClick={handleExpand}
          className="text-gray-400 hover:text-white transition-colors p-1 rounded hover:bg-gray-600"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Messages */}
      <div ref={messagesContainerRef} className="flex-1 overflow-y-auto p-4 space-y-3 custom-scrollbar bg-gray-900">
        {messagesHasMore['general'] && (
          <button
            onClick={loadMoreMessages}
            disabled={messagesLoading['general']}
            className="w-full text-center text-sm text-neonBlue hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed py-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            {messagesLoading['general'] ? (
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Loading...</span>
              </div>
            ) : (
              'Load more messages'
            )}
          </button>
        )}
        
        {(messages['general'] || []).length === 0 && !messagesLoading['general'] ? (
          <div className="text-center py-8">
            <svg className="w-12 h-12 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p className="text-gray-400 text-sm">No messages yet. Be the first to say hello!</p>
          </div>
        ) : (
          (messages['general'] || []).map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender.id === user?.id ? 'justify-end' : 'justify-start'}`}
            >
                          <div
              className={`max-w-[80%] px-3 py-2 rounded-lg ${
                message.sender.id === user?.id
                  ? 'bg-neonBlue text-white'
                  : 'bg-gray-700 text-gray-100 border border-gray-600'
              } ${message.isOptimistic ? 'opacity-70' : ''}`}
            >
              <div className="text-xs opacity-75 mb-1 font-medium">
                {message.sender.username}
                {message.isOptimistic && (
                  <span className="ml-2 text-yellow-300">(sending...)</span>
                )}
              </div>
              <div className="text-sm break-words">{message.content}</div>
              <div className="text-xs opacity-75 mt-1">
                {message.isOptimistic ? 'Sending...' : formatDate(message.createdAt)}
              </div>
            </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={sendMessage} className="p-4 border-t border-gray-600 bg-gray-800">
        <div className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-neonBlue focus:border-transparent text-white placeholder-gray-400"
            disabled={sending}
            maxLength={500}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="px-4 py-2 bg-neonBlue text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center min-w-[44px]"
          >
            {sending ? (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
          </button>
        </div>
        {newMessage.length > 400 && (
          <div className="text-xs text-gray-400 mt-1 text-right">
            {newMessage.length}/500
          </div>
        )}
      </form>
    </div>
  );
};

export default GeneralChatWidget; 