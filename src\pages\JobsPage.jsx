import React, { useState, useEffect } from 'react';
import Navbar from '../components/Navbar';
import { showErrorToast } from '../utils/showErrorToast';
import { showSuccessToast } from '../utils/showSuccessToast';
import { useAuthGuard } from '../hooks/useAuthGuard';
import { factoryService } from '../services/api/factory.service';
import  useUserDataStore  from '../store/useUserDataStore';
import { Link } from 'react-router-dom';
import { AutoMode } from '../types/autoMode';
import WorkResultModal from '../components/factories/WorkResultModal';
import Footer from '../components/common/Footer';
import { Factory, Plus, X, Users, TrendingUp, Settings, DollarSign, UserX } from 'lucide-react';
import SpinnerIcon from '../components/svg/SpinnerIcon';

export default function JobsPage() {
  useAuthGuard();
  const { userData, updateUserData, fetchUserData } = useUserDataStore();
  const [factories, setFactories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [workingFactories, setWorkingFactories] = useState(new Set());
  const [showWorkResultModal, setShowWorkResultModal] = useState(false);
  const [workResult, setWorkResult] = useState(null);
  const [showFactoryDetails, setShowFactoryDetails] = useState(false);
  const [selectedFactory, setSelectedFactory] = useState(null);
  const [factoryWorkers, setFactoryWorkers] = useState([]);
  const [processingAutoMode, setProcessingAutoMode] = useState(false);
  const [updatingWorkerWage, setUpdatingWorkerWage] = useState(false);
  const [updatingDefaultWage, setUpdatingDefaultWage] = useState(false);

  // New state for factory creation
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [createForm, setCreateForm] = useState({
    name: '',
    type: 'GOLD',
    regionId: '',
    wage: 50,
    wageType: 'PERCENTAGE'
  });

  // New state for worker management
  const [workerModalOpen, setWorkerModalOpen] = useState(false);
  const [workerWageForm, setWorkerWageForm] = useState({
    workerId: '',
    wage: 0
  });

  // New state for enhanced features
  const [currentEmployment, setCurrentEmployment] = useState(null);
  const [factoryStats, setFactoryStats] = useState(null);

  // New state for factory default wage update
  const [defaultWageModalOpen, setDefaultWageModalOpen] = useState(false);
  const [defaultWageForm, setDefaultWageForm] = useState({
    wage: 0
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Ensure we have user data
      if (!userData) {
        console.log('No user data, fetching user data');
        await fetchUserData(true);
      }

      // Get the current user data after potential fetch
      const currentUserData = useUserDataStore.getState().userData;
      if (!currentUserData) {
        console.warn('No user data available');
        setFactories([]);
        return;
      }

      if (currentUserData?.region?.id) {
        const factoriesData = await factoryService.getFactoriesInUserRegion();
        setFactories(factoriesData);
      } else {
        console.warn('No region ID found for user:', currentUserData.id);
        setFactories([]);
        // Don't show error toast here as it might be a temporary state during user data refresh
      }
    } catch (error) {
      console.error('Error fetching factory data:', error);
      // Only show error if it's not a region-related issue
      if (!error.message?.includes('region')) {
        showErrorToast('Failed to fetch factory data');
      }
    } finally {
      setLoading(false);
    }
  };

  const loadFactoryDetails = async (factoryId) => {
    try {
      const workers = await factoryService.getFactoryWorkers(factoryId);
      setFactoryWorkers(workers);
    } catch (err) {
      showErrorToast('Failed to load factory details');
    }
  };

  const handleWork = async (factoryId) => {
    if (workingFactories.has(factoryId)) return;

    try {
      setWorkingFactories(prev => new Set(prev).add(factoryId));
      
      // Get the latest energy value
      const currentEnergy = userData?.energy || 0;

      // Use the factory service to work at the factory with the current energy
      const response = await factoryService.workAtFactory(factoryId, currentEnergy);
      // Update user data
      updateUserData(response.user);

      // Store the work session result and show the modal
      setWorkResult(response.workSession);
      setShowWorkResultModal(true);

      showSuccessToast('Work session completed successfully');
      
      // Reload data to update employment status
      await fetchData();
    } catch (error) {
      showErrorToast(error || 'Failed to complete work session');
    } finally {
      setWorkingFactories(prev => {
        const newSet = new Set(prev);
        newSet.delete(factoryId);
        return newSet;
      });
    }
  };

  const handleUpgradeFactory = async (factoryId) => {
    try {
      const upgradeResult = await factoryService.upgradeFactory(factoryId);
      showSuccessToast('Factory upgraded successfully!');
      
      // Update the selected factory with the new data
      if (selectedFactory?.id === factoryId) {
        // Use the complete factory data from the response and add helper methods
        const updatedFactory = {
          ...upgradeResult.factory,
          // Add helper methods for the frontend
          getRequiredExperience: () => upgradeResult.requiredExperience,
          getExperienceProgress: () => upgradeResult.experienceProgress,
          canUpgrade: () => upgradeResult.canUpgrade,
        };
        setSelectedFactory(updatedFactory);
      }
      
      // Refresh the main factory list
      await fetchData();
    } catch (err) {
      showErrorToast(err || 'Failed to upgrade factory');
    }
  };

  const handleShutdownFactory = async (factoryId) => {
    if (!window.confirm('Are you sure you want to shutdown this factory? All workers will be fired and you will get 50% refund of total costs.')) {
      return;
    }

    try {
      const result = await factoryService.shutdownFactory(factoryId);
      showSuccessToast(`Factory shutdown! ${result.workersFired} workers fired. Refund: ${result.refundAmount} gold`);
      await fetchData();
      setShowFactoryDetails(false);
      setSelectedFactory(null);
    } catch (err) {
      showErrorToast(err || 'Failed to shutdown factory');
    }
  };

  const handleFireWorker = async (factoryId, workerId, workerName) => {
    if (!window.confirm(`Are you sure you want to fire ${workerName}?`)) {
      return;
    }

    try {
      const result = await factoryService.fireWorker(factoryId, workerId);
      showSuccessToast(result.message);
      await loadFactoryDetails(factoryId);
      await fetchData();
    } catch (err) {
      showErrorToast(err || 'Failed to fire worker');
    }
  };

  const handleUpdateWorkerWage = async (factoryId, workerId, wage) => {
    setUpdatingWorkerWage(true);
    try {
      await factoryService.updateWorkerWage(factoryId, workerId, { wage, wageType: 'PERCENTAGE' });
      showSuccessToast('Worker wage updated successfully');
      await loadFactoryDetails(factoryId);
    } catch (err) {
      showErrorToast(err || 'Failed to update worker wage');
    } finally {
      setUpdatingWorkerWage(false);
    }
  };

  const openFactoryDetails = async (factory) => {
    setSelectedFactory(factory);
    await loadFactoryDetails(factory.id);
    setShowFactoryDetails(true);
  };

  const isFactoryOwner = (factory) => {
    return userData && factory.ownerId === userData.id;
  };

  const getWageDisplay = (factory) => {
    return `${factory.wage}%`;
  };

  // Helper function to calculate required experience using exponential growth
  const calculateRequiredExperience = (level) => {
    return Math.pow(2, level - 1) * 1000; // Exponential growth: 1000, 2000, 4000, 8000, etc.
  };

  const handleAutoWorkSubmit = async (factoryId) => {
    setProcessingAutoMode(true);

    try {
      // Check if user has premium
      if (!userData?.isPremium) {
        showErrorToast('Auto work mode is only available for premium users.');
        return;
      }

      // Check if user has energy
      if (userData?.energy <= 0) {
        showErrorToast('You do not have enough energy to work at the factory.');
        return;
      }

      // Enable auto mode
      const response = await factoryService.setAutoWorkMode(factoryId, {
        enable: true,
      });

      showSuccessToast('Auto work mode enabled! The system will work every 30 minutes for 24 hours.');

      // If the response includes the first work session, show it in the modal
      if (response.workSession) {
        setWorkResult(response.workSession);
        setShowWorkResultModal(true);
      }

      // Refresh user data to update energy display and auto mode status
      await fetchUserData(true);
    } catch (error) {
      console.error('Failed to enable auto work mode:', error);
      showErrorToast(error || 'Failed to enable auto work mode');
    } finally {
      setProcessingAutoMode(false);
    }
  };

  const handleDisableAutoWork = async (factoryId) => {
    setProcessingAutoMode(true);

    try {
      // Disable auto mode
      await factoryService.setAutoWorkMode(factoryId, {
        enable: false
      });

      showSuccessToast('Auto work mode disabled successfully.');

      // Refresh user data to update auto mode status
      await fetchUserData(true);
    } catch (error) {
      console.error('Failed to disable auto work mode:', error);
      showErrorToast(error || 'Failed to disable auto work mode');
    } finally {
      setProcessingAutoMode(false);
    }
  };

  // Factory creation handlers
  const handleCreateFactory = async (e) => {
    e.preventDefault();
    try {
      await factoryService.createFactory(createForm);
      showSuccessToast('Factory created successfully!');
      setCreateModalOpen(false);
      setCreateForm({
        name: '',
        type: 'GOLD',
        regionId: '',
        wage: 100,
        wageType: 'PERCENTAGE'
      });
      
      // First refresh user data to ensure we have the latest information
      await fetchUserData(true);
      
      // Then fetch factory data
      await fetchData();
    } catch (error) {
      showErrorToast(error || 'Failed to create factory');
    }
  };

  const handleUpdateWorkerWageModal = async (e) => {
    e.preventDefault();
    setUpdatingWorkerWage(true);
    try {
      await factoryService.updateWorkerWage(selectedFactory.id, workerWageForm.workerId, { 
        wage: workerWageForm.wage, 
        wageType: 'PERCENTAGE' 
      });
      showSuccessToast('Worker wage updated successfully!');
      setWorkerModalOpen(false);
      setSelectedFactory(null);
      setWorkerWageForm({
        workerId: '',
        wage: 0
      });
      loadFactoryDetails(selectedFactory.id);
    } catch (error) {
      showErrorToast(error || 'Failed to update worker wage');
    } finally {
      setUpdatingWorkerWage(false);
    }
  };

  const handleUpdateDefaultWage = async (e) => {
    setUpdatingDefaultWage(true);
    e.preventDefault();
    try {
      await factoryService.updateFactory(selectedFactory.id, { 
        wage: defaultWageForm.wage
      });
      showSuccessToast('Factory default wage updated successfully!');
      setDefaultWageModalOpen(false);
      setDefaultWageForm({
        wage: 0
      });
      
      // Refresh all factory data first
      await fetchData();
      
      // Then get the updated factory with all relations
      const updatedFactory = await factoryService.getFactory(selectedFactory.id);
      setSelectedFactory(updatedFactory);
      
      // If factory details modal is open, refresh the workers data too
      if (showFactoryDetails) {
        await loadFactoryDetails(selectedFactory.id);
      }
    } catch (error) {
      showErrorToast(error || 'Failed to update factory default wage');
    } finally {
      setUpdatingDefaultWage(false);
    }
  };

  // Initialize create form with user's region when modal opens
  const openCreateModal = () => {
    if (userData?.region?.id) {
      setCreateForm({
        name: '',
        type: 'GOLD',
        regionId: userData.region.id.toString(),
        wage: 50,
        wageType: 'PERCENTAGE'
      });
    }
    setCreateModalOpen(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading jobs...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header with Create Factory Button */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center">
                <Factory className="w-8 h-8 text-neonBlue mr-3" />
                Jobs & Factories
              </h1>
              <p className="mt-2 text-gray-400">Work at factories or create your own</p>
            </div>
            <button
              onClick={openCreateModal}
              className="bg-neonBlue hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Factory
            </button>
          </div>
        </div>

        {/* Energy Status */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">Your Energy</h2>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
              <span className="text-white text-lg">{userData?.energy || 0}/{userData.isPremium ? 200 : 100}</span>
            </div>
          </div>
          <div className="mt-2 w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((userData?.energy || 0) / (userData?.isPremium ? 200 : 100)) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Current Employment Status */}
        {userData?.workingAt && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-white">Current Employment</h2>
                <p className="text-gray-300">Working at: {userData.workingAt.name}</p>
                <p className="text-gray-400 text-sm">
                  Wage: {getWageDisplay(userData.workingAt)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Auto Work Info */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-3">Auto Work Mode</h2>
          <div className="bg-gray-700 p-4 rounded-md">
            <p className="text-gray-300 mb-2">
              <span className="text-neonBlue font-medium">Premium users</span> can enable auto work mode for factories. When enabled:
            </p>
            <ul className="list-disc list-inside text-gray-300 space-y-1 ml-2">
              <li>The system will automatically use all available energy to work at the factory every 30 minutes</li>
              <li>Auto work will continue for 24 hours, after which it will automatically stop</li>
              <li>Auto work will stop if the factory reaches its maximum worker capacity</li>
              <li>You can only have one auto mode active at a time (either auto work or auto war participation)</li>
            </ul>

            {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId && (
              <div className="mt-4 p-3 bg-green-800 bg-opacity-30 rounded-md border border-green-600">
                <p className="text-green-400 font-medium mb-1">Auto Work Mode is Currently Active</p>
                <p className="text-gray-300 text-sm">
                  You have auto work enabled for factory ID: {userData.autoTargetId}
                  {userData?.autoModeExpiresAt && (
                    <span className="block mt-1">
                      Expires: {new Date(userData.autoModeExpiresAt).toLocaleString()}
                    </span>
                  )}
                </p>
              </div>
            )}

            {userData?.activeAutoMode === AutoMode.WAR && (
              <div className="mt-4 p-3 bg-yellow-800 bg-opacity-30 rounded-md border border-yellow-600">
                <p className="text-yellow-400 font-medium">Auto War Mode is Currently Active</p>
                <p className="text-gray-300 text-sm mt-1">
                  
                </p>
              </div>
            )}

            {!userData?.isPremium && (
              <div className="mt-4 p-3 bg-gray-600 rounded-md text-center">
                <p className="text-yellow-400 mb-2">This feature requires a premium account</p>
                <Link to="/shop" className="text-neonBlue hover:text-blue-400">
                  Upgrade to Premium
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Available Factories */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-white mb-4">Available Factories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {factories.map((factory) => (
              <div
                key={factory.id}
                className={`bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors cursor-pointer ${
                  factory.isPrivate ? 'border-2 border-purple-500' : ''
                }`}
                onClick={() => setSelectedFactory(factory)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-white">{factory.name}</h3>
                  <div className="flex flex-col items-end gap-1">
                    <span className="px-2 py-1 rounded text-sm bg-yellow-500 text-black">
                      GOLD
                    </span>
                    {factory.isPrivate && (
                      <span className="bg-purple-600 text-white px-2 py-1 rounded text-xs">
                        Private
                      </span>
                    )}
                  </div>
                </div>
                <div className="space-y-2 text-gray-300">
                  <div className="flex justify-between">
                    <span>Level:</span>
                    <span className="text-white">{factory.level}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Experience:</span>
                    <span className="text-white">{factory.experience}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Resource Bonus:</span>
                    <span className="text-white">{factory.resourceBonus}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Default Wage:</span>
                    <span className="text-white">{getWageDisplay(factory)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Workers:</span>
                    <span className="text-white">{factory.workers?.length || 0}/{factory.maxWorkers}</span>
                  </div>
                </div>
                <div className="space-y-2 mt-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleWork(factory.id);
                    }}
                    disabled={!userData || workingFactories.has(factory.id)}
                    className={`w-full px-4 py-2 rounded-md text-white ${
                      !userData || workingFactories.has(factory.id)
                        ? 'bg-gray-500 cursor-not-allowed'
                        : 'bg-neonBlue hover:bg-blue-600'
                    }`}
                  >
                    {workingFactories.has(factory.id) ? 'Working...' : 'Work Here'}
                  </button>

                  <div className="flex gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        openFactoryDetails(factory);
                      }}
                      className="flex-1 px-3 py-2 bg-gray-600 text-white rounded hover:bg-gray-500 text-sm"
                    >
                      Details
                    </button>
                    {/* {isFactoryOwner(factory) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedFactory(factory);
                          setWorkerModalOpen(true);
                        }}
                        className="flex-1 px-3 py-2 bg-green-600 text-white rounded hover:bg-green-500 text-sm"
                      >
                        Manage
                      </button>
                    )} */}
                  </div>

                  {/* Auto Work Mode Section */}
                  <div className="border-t border-gray-600 pt-3 mt-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">Auto Work Mode</span>
                      {!userData?.isPremium && (
                        <span className="text-xs text-yellow-400">(Premium Only)</span>
                      )}
                      {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString() && (
                        <span className="text-xs text-green-400">Active</span>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAutoWorkSubmit(factory.id);
                        }}
                        disabled={!userData || !userData.isPremium || processingAutoMode ||
                          userData?.activeAutoMode === AutoMode.WORK}
                        className={`flex-1 px-3 py-1 text-sm rounded-md ${
                          !userData || !userData.isPremium ||
                          userData?.activeAutoMode === AutoMode.WORK
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-green-600 hover:bg-green-700 text-white'
                        }`}
                      >
                        {processingAutoMode ? 'Processing...' : 'Enable Auto'}
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDisableAutoWork(factory.id);
                        }}
                        disabled={!userData || !userData.isPremium || processingAutoMode ||
                          !(userData?.activeAutoMode === AutoMode.WORK)}
                        className={`flex-1 px-3 py-1 text-sm rounded-md ${
                          !userData || !userData.isPremium ||
                          !(userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString())
                            ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                            : 'bg-red-600 hover:bg-red-700 text-white'
                        }`}
                      >
                        {processingAutoMode ? 'Processing...' : 'Disable Auto'}
                      </button>
                    </div>

                    {!userData?.isPremium && (
                      <div className="mt-2 text-xs text-center">
                        <Link to="/shop" className="text-neonBlue hover:text-blue-400">
                          Upgrade to Premium
                        </Link>
                      </div>
                    )}

                    {userData?.activeAutoMode === AutoMode.WORK && userData?.autoTargetId === factory.id.toString() && (
                      <div className="mt-2 text-xs text-center text-green-400">
                        Auto work is active for this factory
                        {userData?.autoModeExpiresAt && (
                          <div className="mt-1">
                            Expires: {new Date(userData.autoModeExpiresAt).toLocaleString()}
                          </div>
                        )}
                      </div>
                    )}

                    {userData?.activeAutoMode === AutoMode.WAR && (
                      <div className="mt-2 text-xs text-center text-yellow-400">
                        You have auto mode active for war participation
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Enhanced Factory Details Modal */}
      {showFactoryDetails && selectedFactory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto custom-scrollbar border border-gray-700">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center">
                <Factory className="w-6 h-6 text-neonBlue mr-2" />
                {selectedFactory.name} - Factory Management
              </h2>
              <button
                onClick={() => setShowFactoryDetails(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Factory Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-700 p-4 rounded">
                <h3 className="font-semibold mb-3 text-white flex items-center">
                  <DollarSign className="w-4 h-4 text-neonBlue mr-2" />
                  Basic Info
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className={`px-2 py-1 rounded text-xs ${
                      selectedFactory.type === 'GOLD' ? 'bg-yellow-500 text-black' : 'bg-green-500 text-white'
                    }`}>
                      {selectedFactory.type}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Level:</span>
                    <span className="text-white font-semibold">{selectedFactory.level}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Experience:</span>
                    <span className="text-white">{selectedFactory.experience.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Resource Bonus:</span>
                    <span className="text-green-400">+{selectedFactory.resourceBonus}%</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded">
                <h3 className="font-semibold mb-3 text-white flex items-center">
                  <Users className="w-4 h-4 text-neonBlue mr-2" />
                  Workers
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Current:</span>
                    <span className="text-white">{selectedFactory.workers?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Maximum:</span>
                    <span className="text-white">{selectedFactory.maxWorkers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Available:</span>
                    <span className="text-green-400">{selectedFactory.maxWorkers - (selectedFactory.workers?.length || 0)}</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded">
                <h3 className="font-semibold mb-3 text-white flex items-center">
                  <TrendingUp className="w-4 h-4 text-neonBlue mr-2" />
                  Production
                </h3>
                <div className="space-y-2 text-sm">
                  {/* <div className="flex justify-between">
                    <span className="text-gray-400">Energy Cost:</span>
                    <span className="text-white">{selectedFactory.energyCost}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Resource/Work:</span>
                    <span className="text-white">{selectedFactory.resourcePerWork}</span>
                  </div> */}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Default Wage:</span>
                    <span className="text-white">{getWageDisplay(selectedFactory)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Experience Progress Bar */}
            {selectedFactory.level < 100 && (
              <div className="bg-gray-700 p-4 rounded mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-white">Experience Progress</h3>
                  <span className="text-sm text-gray-400">
                    {selectedFactory.experience.toLocaleString()} / {selectedFactory.getRequiredExperience?.() || calculateRequiredExperience(selectedFactory.level)} XP
                  </span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${Math.min((selectedFactory.experience / (selectedFactory.getRequiredExperience?.() || calculateRequiredExperience(selectedFactory.level))) * 100, 100)}%` 
                    }}
                  ></div>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {selectedFactory.experience >= (selectedFactory.getRequiredExperience?.() || calculateRequiredExperience(selectedFactory.level)) 
                    ? 'Ready to upgrade!' 
                    : `Need ${((selectedFactory.getRequiredExperience?.() || calculateRequiredExperience(selectedFactory.level)) - selectedFactory.experience).toLocaleString()} more XP to upgrade`
                  }
                </p>
              </div>
            )}

            {/* Owner Actions */}
            {isFactoryOwner(selectedFactory) && (
              <div className="bg-gray-700 p-4 rounded mb-6">
                <h3 className="font-semibold mb-3 text-white">Owner Actions</h3>
                <div className="flex flex-wrap gap-3">
                  {selectedFactory.level < 100 && selectedFactory.experience >= (selectedFactory.getRequiredExperience?.() || calculateRequiredExperience(selectedFactory.level)) && (
                    <button
                      onClick={() => handleUpgradeFactory(selectedFactory.id)}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded flex items-center text-sm"
                    >
                      <TrendingUp className="w-4 h-4 mr-2" />
                      Upgrade Factory (35 Gold)
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setDefaultWageForm({ wage: selectedFactory.wage });
                      setDefaultWageModalOpen(true);
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center text-sm"
                  >
                    <DollarSign className="w-4 h-4 mr-2" />
                    Update Default Wage
                  </button>
                  {/* <button
                    onClick={() => {
                      setSelectedFactory(selectedFactory);
                      setWorkerModalOpen(true);
                    }}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center text-sm"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Manage Workers
                  </button> */}
                  <button
                    onClick={() => handleShutdownFactory(selectedFactory.id)}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded flex items-center text-sm"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Shutdown Factory
                  </button>
                </div>
              </div>
            )}

            {/* Workers Table */}
            <div className="bg-gray-700 rounded overflow-hidden">
              <div className="px-4 py-3 border-b border-gray-600">
                <h3 className="font-semibold text-white flex items-center">
                  <Users className="w-4 h-4 text-neonBlue mr-2" />
                  Workers ({factoryWorkers.length})
                </h3>
              </div>
              {factoryWorkers.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-600">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Worker</th>
                        {isFactoryOwner(selectedFactory) && (
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Wage</th>
                        )}
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Joined</th>
                        {isFactoryOwner(selectedFactory) && (
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        )}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-600">
                      {factoryWorkers.map((worker) => (
                        <tr key={worker.id} className="hover:bg-gray-600">
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm font-medium text-white">
                              {worker.worker?.username || `User ${worker.workerId}`}
                            </div>
                          </td>
                          {isFactoryOwner(selectedFactory) && (
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className="px-2 py-1 text-xs rounded bg-green-500 text-white">
                                {worker.wage}%
                              </span>
                            </td>
                          )}
                          <td className="px-4 py-3 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded ${
                              worker.isActive ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'
                            }`}>
                              {worker.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                            {new Date(worker.createdAt).toLocaleDateString()}
                          </td>
                          {isFactoryOwner(selectedFactory) && (
                            <td className="px-4 py-3 whitespace-nowrap text-sm">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => {
                                    setSelectedFactory(selectedFactory);
                                    setWorkerWageForm({
                                      workerId: worker.workerId,
                                      wage: worker.wage
                                    });
                                    setWorkerModalOpen(true);
                                  }}
                                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs"
                                >
                                  Edit Wage
                                </button>
                                {worker.isActive && (
                                  <button
                                    onClick={() => handleFireWorker(
                                      selectedFactory.id, 
                                      worker.workerId, 
                                      worker.worker?.username || `User ${worker.workerId}`
                                    )}
                                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs flex items-center"
                                  >
                                    <UserX className="w-3 h-3 mr-1" />
                                    Fire
                                  </button>
                                )}
                              </div>
                            </td>
                          )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="px-4 py-8 text-center">
                  <Users className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">No workers yet</p>
                  <p className="text-sm text-gray-500">Workers will appear here when they start working at this factory</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create Factory Modal */}
      {createModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Factory className="w-5 h-5 text-neonBlue mr-2" />
                Create New Factory
              </h2>
              <button
                onClick={() => setCreateModalOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <form onSubmit={handleCreateFactory} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Factory Name</label>
                <input
                  type="text"
                  value={createForm.name}
                  onChange={(e) => setCreateForm({...createForm, name: e.target.value})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-neonBlue"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Region</label>
                <input
                  type="text"
                  value={userData?.region?.name || 'Unknown Region'}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-600 text-gray-300 cursor-not-allowed"
                  disabled
                />
                <p className="text-xs text-gray-400 mt-1">
                  Factory will be created in your current region
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Worker Wage (%)</label>
                <input
                  type="number"
                  value={createForm.wage}
                  onChange={(e) => setCreateForm({...createForm, wage: parseFloat(e.target.value)})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-neonBlue"
                  min="0"
                  max="100"
                  step="1"
                  required
                />
                <p className="text-xs text-gray-400 mt-1">Percentage of additional resources (money, ore) workers will earn. Gold goes 100% to workers.</p>
              </div>
              <div className="bg-yellow-900/20 border border-yellow-700/50 rounded-md p-3">
                <p className="text-sm text-yellow-400">
                  <strong>Creation Cost:</strong> 200 Gold
                </p>
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setCreateModalOpen(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-md font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-neonBlue hover:bg-blue-600 text-white py-2 px-4 rounded-md font-medium transition-colors"
                >
                  Create Factory
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Worker Management Modal */}
      {workerModalOpen && selectedFactory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <Users className="w-5 h-5 text-neonBlue mr-2" />
                Update Worker Wage
              </h2>
              <button
                onClick={() => setWorkerModalOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="bg-gray-700 p-3 rounded mb-4">
              <p className="text-sm text-gray-300">
                <strong>Factory:</strong> {selectedFactory.name}
              </p>
              <p className="text-sm text-gray-300">
                <strong>Worker:</strong> {factoryWorkers.find(w => w.workerId === workerWageForm.workerId)?.worker?.username || `User ${workerWageForm.workerId}`}
              </p>
            </div>

            <form onSubmit={handleUpdateWorkerWageModal} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">Worker Wage (%)</label>
                <input
                  type="number"
                  value={workerWageForm.wage}
                  onChange={(e) => setWorkerWageForm({...workerWageForm, wage: parseFloat(e.target.value)})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-neonBlue"
                  min="0"
                  max="100"
                  step="1"
                  required
                />
                <p className="text-xs text-gray-400 mt-1">
                  Percentage of additional resources (money, ore) the worker will earn. Gold goes 100% to workers.
                </p>
              </div>
              <div className="bg-blue-900/20 border border-blue-700/50 rounded-md p-3">
                <p className="text-sm text-blue-400">
                  <strong>Current Wage:</strong> {workerWageForm.wage}%
                </p>
                <p className="text-sm text-blue-400 mt-1">
                  <strong>Explanation:</strong> Worker will receive {workerWageForm.wage}% of additional resources (money, ore). Gold goes 100% to workers.
                </p>
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setWorkerModalOpen(false)}
                  className="flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-md font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={updatingWorkerWage}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-green-800 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md font-medium transition-colors flex items-center justify-center"
                >
                  {updatingWorkerWage ? (
                    <>
                      <SpinnerIcon className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" />
                      Updating...
                    </>
                  ) : (
                    'Update Wage'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Default Wage Update Modal */}
      {defaultWageModalOpen && selectedFactory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg max-w-md w-full p-6 border border-gray-700">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <DollarSign className="w-5 h-5 text-neonBlue mr-2" />
                Update Factory Default Wage
              </h2>
              <button
                onClick={() => {
                  setDefaultWageModalOpen(false);
                  setDefaultWageForm({ wage: 0 });
                }}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="bg-gray-700 p-3 rounded mb-4">
              <p className="text-sm text-gray-300">
                <strong>Factory:</strong> {selectedFactory.name}
              </p>
              <p className="text-sm text-gray-300">
                <strong>Current Default Wage:</strong> {selectedFactory.wage}%
              </p>
            </div>

            <form onSubmit={handleUpdateDefaultWage} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">New Default Wage (%)</label>
                <input
                  type="number"
                  value={defaultWageForm.wage || selectedFactory.wage}
                  onChange={(e) => setDefaultWageForm({...defaultWageForm, wage: parseFloat(e.target.value)})}
                  className="w-full border border-gray-600 rounded-md px-3 py-2 bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-neonBlue"
                  min="0"
                  max="100"
                  step="1"
                  required
                />
                <p className="text-xs text-gray-400 mt-1">
                  This will be the default wage for new workers joining the factory. Existing workers keep their individual wages.
                </p>
              </div>
              <div className="bg-blue-900/20 border border-blue-700/50 rounded-md p-3">
                <p className="text-sm text-blue-400">
                  <strong>New Default Wage:</strong> {defaultWageForm.wage}%
                </p>
                <p className="text-sm text-blue-400 mt-1">
                  <strong>Explanation:</strong> New workers will receive {defaultWageForm.wage}% of additional resources (money, ore). Gold goes 100% to workers.
                </p>
              </div>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setDefaultWageModalOpen(false);
                    setDefaultWageForm({ wage: 0 });
                  }}
                  className="flex-1 bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-md font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={updatingDefaultWage}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md font-medium transition-colors flex items-center justify-center"
                >
                  {updatingDefaultWage ? (
                    <>
                      <SpinnerIcon className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" />
                      Updating...
                    </>
                  ) : (
                    'Update Default Wage'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Work Result Modal */}
      <WorkResultModal
        isOpen={showWorkResultModal}
        onClose={() => setShowWorkResultModal(false)}
        workSession={workResult}
      />
       <Footer />
    </div>
  );
}
