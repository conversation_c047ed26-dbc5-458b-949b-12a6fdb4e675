import React, { useState } from 'react';
import { warService } from '../../services/api/war.service';
import { WarType, WarTarget } from '../../types/war.ts';
import { showSuccessToast } from '../../utils/showSuccessToast';
import { showErrorToast } from '../../utils/showErrorToast';
import { FaFistRaised, FaTimes, FaCoins, FaUsers, FaClock } from 'react-icons/fa';
import SpinnerIcon from '../svg/SpinnerIcon';
import FireIcon from '../svg/FireIcon';

const RevolutionWarModal = ({ isOpen, onClose, region, userData, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [declaration, setDeclaration] = useState('');
  
  if (!isOpen) return null;
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      if (!userData || userData.level < 2) {
        throw new Error('You must be level 2 or higher to start a revolution');
      }
      if (!userData || userData.gold < 200) {
        throw new Error('You need 200 gold to start a revolution');
      }
      if (!region?.users?.some(user => user.id === userData.id)) {
        throw new Error('You must be a citizen of this region to start a revolution');
      }
      if (region.lastRevolution) {
        const lastRevolutionDate = new Date(region.lastRevolution);
        const fourDaysAgo = new Date();
        fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
        if (lastRevolutionDate > fourDaysAgo) {
          throw new Error('This region is still in revolution cooldown period');
        }
      }
      const warData = {
        warType: WarType.REVOLUTION,
        warTarget: WarTarget.REVOLUTION,
        declaration: declaration.trim(),
        attackerRegionId: region.id,
        defenderRegionId: region.id,
      };
      const createdWar = await warService.declareWar(warData);
      showSuccessToast(`Revolution declared in ${region.name}!`);
      if (onSuccess) onSuccess(createdWar);
    } catch (err) {
      console.error('Error declaring revolution:', err);
      showErrorToast(err.response?.data?.message || err.message || 'Failed to declare revolution');
    } finally {
      setLoading(false);
    }
  };
  
  const canStartRevolution = () => {
    if (!userData || !region) return false;
    const isUserCitizen = region.users?.some(user => user.id === userData.id);
    if (!isUserCitizen) return false;
    if (userData.level < 2) return false;
    if (userData.gold < 200) return false;
    if (region.lastRevolution) {
      const lastRevolutionDate = new Date(region.lastRevolution);
      const fourDaysAgo = new Date();
      fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
      if (lastRevolutionDate > fourDaysAgo) return false;
    }
    return true;
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-700">
          <div className="flex items-center">
            <div className="bg-gradient-to-r from-red-800 to-red-900 p-3 rounded-xl mr-4">
              <FaFistRaised className="text-red-300 text-2xl" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">START REVOLUTION</h2>
              <p className="text-gray-400">Overthrow the current leadership</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Region Info */}
          <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5 mb-6">
            <h3 className="text-lg font-bold text-white mb-4 border-b border-gray-700 pb-3">REVOLUTION TARGET</h3>
            <div className="flex items-center gap-4">
              {region?.imageUrl && (
                <div className="border-2 border-gray-600 rounded-xl overflow-hidden">
                  <img
                    src={region.imageUrl}
                    alt={region.name}
                    className="w-16 h-16 object-cover"
                  />
                </div>
              )}
              <div>
                <h4 className="text-white font-bold text-xl">{region?.name}</h4>
                {region?.state && (
                  <p className="text-gray-400 text-sm mt-1">
                    Currently part of {region.state.name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-5 mb-6">
            <h3 className="text-lg font-bold text-white mb-4 border-b border-gray-700 pb-3">REQUIREMENTS</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between bg-gray-800/50 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-yellow-900/30 p-2 rounded-lg mr-3">
                    <FaCoins className="text-yellow-400" />
                  </div>
                  <span className="text-gray-300">Cost</span>
                </div>
                <span className={`text-xl font-bold ${userData?.gold >= 200 ? 'text-green-400' : 'text-red-400'}`}>
                  200 Gold {userData?.gold >= 200 ? '✓' : '✗'}
                </span>
              </div>

              <div className="flex items-center justify-between bg-gray-800/50 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-blue-900/30 p-2 rounded-lg mr-3">
                    <FaUsers className="text-blue-400" />
                  </div>
                  <span className="text-gray-300">Level Requirement</span>
                </div>
                <span className={`text-xl font-bold ${userData?.level >= 2 ? 'text-green-400' : 'text-red-400'}`}>
                  Level 2+ {userData?.level >= 5 ? '✓' : '✗'}
                </span>
              </div>

              <div className="flex items-center justify-between bg-gray-800/50 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-purple-900/30 p-2 rounded-lg mr-3">
                    <FaUsers className="text-purple-400" />
                  </div>
                  <span className="text-gray-300">Citizenship</span>
                </div>
                <span className={`text-xl font-bold ${region?.users?.some(user => user.id === userData?.id) ? 'text-green-400' : 'text-red-400'}`}>
                  Region Citizen {region?.users?.some(user => user.id === userData?.id) ? '✓' : '✗'}
                </span>
              </div>

              <div className="flex items-center justify-between bg-gray-800/50 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-orange-900/30 p-2 rounded-lg mr-3">
                    <FaClock className="text-orange-400" />
                  </div>
                  <span className="text-gray-300">Cooldown</span>
                </div>
                <span className={`text-xl font-bold ${!region?.lastRevolution || (() => {
                  const lastRevolutionDate = new Date(region.lastRevolution);
                  const fourDaysAgo = new Date();
                  fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
                  return lastRevolutionDate <= fourDaysAgo;
                })() ? 'text-green-400' : 'text-red-400'}`}>
                  Available {!region?.lastRevolution || (() => {
                    const lastRevolutionDate = new Date(region.lastRevolution);
                    const fourDaysAgo = new Date();
                    fourDaysAgo.setDate(fourDaysAgo.getDate() - 4);
                    return lastRevolutionDate <= fourDaysAgo;
                  })() ? '✓' : '✗'}
                </span>
              </div>
            </div>
          </div>

          {/* Revolution Info */}
          <div className="bg-gradient-to-br from-red-900/20 to-red-900/10 border-2 border-red-800/30 rounded-xl p-5 mb-6">
            <h3 className="text-lg font-bold text-white mb-4 border-b border-red-800/30 pb-3">REVOLUTION EFFECTS</h3>
            <ul className="text-gray-300 space-y-3">
              <li className="flex items-start">
                <FireIcon />
                <span>Duration: <span className="text-white font-bold">24 hours</span></span>
              </li>
              <li className="flex items-start">
                <FireIcon />
                <span>Only citizens of this region can participate</span>
              </li>
              <li className="flex items-start">
                <FireIcon />
                <span>If attackers win: Region becomes independent state</span>
              </li>
              <li className="flex items-start">
                <FireIcon />
                <span>If defenders win: No changes to region ownership</span>
              </li>
              <li className="flex items-start">
                <FireIcon />
                <span>New state will have immediate elections if created</span>
              </li>
            </ul>
          </div>

          {/* Declaration Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="flex gap-3 pt-2">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 py-3 px-4 rounded-xl font-bold transition-all bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white border-2 border-gray-600"
              >
                CANCEL
              </button>
              <button
                type="submit"
                disabled={loading || !canStartRevolution()}
                className={`flex-1 py-3 px-4 rounded-xl font-bold transition-all ${
                  canStartRevolution() && !loading
                    ? 'bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white'
                    : 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-500 cursor-not-allowed'
                } border-2 border-red-800 shadow-lg shadow-red-900/30`}
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <SpinnerIcon />
                    STARTING...
                  </span>
                ) : 'START REVOLUTION'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RevolutionWarModal;