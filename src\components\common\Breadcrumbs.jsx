import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';

const Breadcrumbs = () => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter(x => x);

  const breadcrumbMap = {
    'home': 'Home',
    'profile': 'Profile',
    'map': 'World Map',
    'jobs': 'Jobs',
    'shop': 'Shop',
    'party': 'Parties',
    'create': 'Create',
    'states': 'States',
    'wars': 'Wars',
    'new': 'New',
    'analytics': 'Analytics',
    'users': 'Users',
    'regions': 'Regions',
    'travel': 'Travel',
    'permissions': 'Permissions',
    'payment': 'Payment',
    'success': 'Success',
    'cancel': 'Cancelled',
    'login': 'Login',
    'register': 'Register',
    'forgot-password': 'Forgot Password',
    'reset-password': 'Reset Password',
    'verify-account': 'Verify Account'
  };

  const generateBreadcrumbItems = () => {
    const items = [
      {
        name: 'Home',
        path: '/',
        icon: <Home className="h-4 w-4" />
      }
    ];

    let currentPath = '';
    
    pathnames.forEach((name, index) => {
      currentPath += `/${name}`;
      
      // Skip numeric IDs for cleaner breadcrumbs
      if (!isNaN(name)) {
        return;
      }

      const displayName = breadcrumbMap[name] || name.charAt(0).toUpperCase() + name.slice(1);
      
      items.push({
        name: displayName,
        path: currentPath,
        isLast: index === pathnames.length - 1
      });
    });

    return items;
  };

  const breadcrumbItems = generateBreadcrumbItems();

  if (breadcrumbItems.length <= 1) {
    return null;
  }

  return (
    <nav className="bg-gray-800 border-b border-gray-700" aria-label="Breadcrumb">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol className="flex items-center space-x-2 py-3">
          {breadcrumbItems.map((item, index) => (
            <li key={item.path} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-500 mx-2" />
              )}
              
              {item.isLast ? (
                <span className="text-gray-300 font-medium flex items-center">
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  {item.name}
                </span>
              ) : (
                <Link
                  to={item.path}
                  className="text-gray-400 hover:text-neonBlue transition-colors flex items-center"
                >
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  {item.name}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
};

export default Breadcrumbs; 