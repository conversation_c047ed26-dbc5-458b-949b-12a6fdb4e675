import React from 'react';
import { FaExclamationTriangle, FaSpinner } from 'react-icons/fa';

const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  confirmButtonClass = "bg-blue-600 hover:bg-blue-700",
  isLoading = false,
  loadingText = "Processing...",
  showWarning = false,
  children
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex items-center mb-4">
          {showWarning && (
            <FaExclamationTriangle className="text-yellow-400 mr-3 text-xl" />
          )}
          <h3 className="text-xl font-semibold text-white">
            {title}
          </h3>
        </div>
        
        <div className="mb-6">
          <p className="text-gray-300 mb-3">
            {message}
          </p>
          {children}
        </div>

        <div className="flex space-x-4">
          <button
            onClick={onClose}
            disabled={isLoading}
            className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            disabled={isLoading}
            className={`flex-1 px-4 py-2 ${confirmButtonClass} disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center`}
          >
            {isLoading ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                {loadingText}
              </>
            ) : (
              confirmText
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal; 