import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import Navbar from "../components/Navbar";
import { userService } from "../services/api/user.service";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import UserWarStats from "../components/analytics/UserWarStats";
import { User } from "../types/user";
import { Region } from "../types/region";
import { State } from "../types/state";
import useUserDataStore from "../store/useUserDataStore";
import useChatStore from "../store/useChatStore";
import ChatInterface from "../components/chat/ChatInterface";
import GiftPremiumModal from "../components/payments/GiftPremiumModal";
import { 
  <PERSON><PERSON><PERSON>, 
  Brain, 
  ShieldAlt, 
  LevelUpAlt, 
  Envelope,
  Crown,
  Coins,
  Flag,
  Users,
  GlobeAmericas,
  ChartLine,
  Landmark,
  UserFa,
  DollarSign,
} from "../components/svg/svgs";

export default function UserProfile() {
  const { id } = useParams<{ id: string }>();
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [region, setRegion] = useState<Region | null>(null);
  const [userState, setUserState] = useState<State | null>(null);
  const {
    userData: currentUser,
    loading: userDataLoading,
    fetchUserData,
  } = useUserDataStore();
  const { setActiveChat, createOrFindDirectChat } = useChatStore();

  const [transferAmount, setTransferAmount] = useState<number>(100);
  const [showTransferModal, setShowTransferModal] = useState<boolean>(false);
  const [showGiftPremiumModal, setShowGiftPremiumModal] = useState<boolean>(false);
  const [isCreatingChat, setIsCreatingChat] = useState<boolean>(false);
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);

  // Check if the user is viewing their own profile
  const isOwnProfile =
    currentUser?.id && id && currentUser.id.toString() === id;

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        if (!id) return;
        const userData = await userService.getUserById(id);
        setProfile(userData);

        if (userData?.region) {
          setRegion(userData.region);
        }
        // Check if user has a state through their region
        if (userData?.region?.state) {
          setUserState(userData.region.state);
        }
      } catch (error) {
        showErrorToast(error || "Failed to load user profile");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchUserProfile();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-blue-400 text-xl animate-pulse">
            Loading profile...
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
        <Navbar />
        <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-red-500 text-xl mb-4">User not found</div>
          <Link
            to="/home"
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  // Calculate level progress
  const xpForCurrentLevel = Math.pow(profile?.level || 1, 2) * 100;
  const xpForNextLevel = Math.pow((profile?.level || 1) + 1, 2) * 100;
  const xpIntoLevel = (profile?.experience || 0) - xpForCurrentLevel;
  const xpNeeded = xpForNextLevel - xpForCurrentLevel;
  const levelBar = Math.floor((xpIntoLevel / xpNeeded) * 100);

  // Transfer modal component
  const TransferMoneyModal = () => {
    const [amount, setAmount] = useState<number>(transferAmount);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

    const handleTransfer = async () => {
      try {
        setIsSubmitting(true);
        await userService.sendMoney({
          fromUserId: currentUser.id,
          toUserId: Number(id),
          amount: amount,
        });

        fetchUserData(true);
        showSuccessToast("Money sent successfully!");
        setShowTransferModal(false);
      } catch (error) {
        if (error?.response?.status === 409) {
          showErrorToast("Insufficient funds for this transfer");
        } else if (error?.response?.status === 400) {
          showErrorToast("Invalid transfer request");
        } else if (error?.response?.status === 404) {
          showErrorToast("User not found or not active");
        } else {
          showErrorToast(error?.message || "Failed to send money");
        }
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl max-w-md w-full p-6 border border-gray-700/50">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center">
            <DollarSign className="mr-2 text-green-400" />
            Send Money to {profile?.username}
          </h2>

          <div className="mb-4">
            <label
              htmlFor="transferAmount"
              className="block text-gray-400 mb-2"
            >
              Amount
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <DollarSign className="text-green-400" />
              </div>
              <input
                id="transferAmount"
                type="number"
                min="0.01"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                className="bg-gray-700/50 text-white rounded-xl py-3 pl-10 pr-4 w-full focus:outline-none focus:ring-2 focus:ring-blue-500/50"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowTransferModal(false)}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-xl transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleTransfer}
              disabled={amount <= 0 || isSubmitting}
              className={`bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-green-500/20 ${
                amount <= 0 || isSubmitting
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  Processing...
                </>
              ) : (
                <>
                  <DollarSign className="mr-2" />
                  Send Money
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const handleSendMessage = async () => {
    if (!profile || !currentUser || isCreatingChat) return;

    setIsCreatingChat(true);
    try {
      const chat = await createOrFindDirectChat(profile.id);
      setActiveChat(chat.id);
      setIsChatOpen(true);
    } catch (error: any) {
      showErrorToast(error.message || "Failed to open chat");
    } finally {
      setIsCreatingChat(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
      <Navbar />

      {/* Own Profile Notice */}
      {isOwnProfile && (
        <div className="bg-blue-600/10 border-b border-blue-500/30">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="text-blue-400 mr-2">ℹ️</div>
                <span className="text-white">
                  This is your public profile that others can see.
                </span>
              </div>
              <Link
                to="/profile"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
              >
                Edit Profile
              </Link>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="bg-gray-800/70 backdrop-blur-md rounded-xl shadow-2xl p-6 mb-8 border border-gray-700/50 relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-48 h-48 bg-blue-600/10 rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-600/10 rounded-full blur-3xl translate-y-1/2 -translate-x-1/2"></div>

          <div className="flex flex-col md:flex-row items-center md:items-start">
            {/* Avatar Section */}
            <div className="relative mb-4 md:mb-0">
              <div className="w-28 h-28 bg-gradient-to-br from-blue-900/30 to-indigo-900/30 rounded-full flex items-center justify-center text-4xl text-blue-400 overflow-hidden relative border-2 border-blue-500/30 shadow-lg">
                {profile?.avatarUrl ? (
                  <img
                    src={profile.avatarUrl}
                    alt="Profile Avatar"
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <span className="text-4xl font-bold">
                    {profile?.username?.charAt(0).toUpperCase() || "U"}
                  </span>
                )}
              </div>

              {/* Premium Crown Badge - تم تعديل موضعه */}
              {profile?.isPremium && (
                <div className="absolute top-0 right-0 transform -translate-y-1/4 translate-x-1/4 bg-gradient-to-r from-yellow-600 to-amber-600 border border-yellow-500/30 text-yellow-100 rounded-full w-8 h-8 flex items-center justify-center backdrop-blur-sm shadow-lg z-10">
                  <Crown className="text-yellow-300 text-sm" />
                </div>
              )}
            </div>

            <div className="md:ml-6 text-center md:text-left flex-grow">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                <div>
                  <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400 flex items-center justify-center md:justify-start">
                    {profile?.username}
                  </h1>
                  <p className="text-gray-400">
                    Member since{" "}
                    {new Date(profile?.createdAt).toLocaleDateString()}
                  </p>
                  {profile?.aboutMe && (
                    <p className="text-white mt-2 max-w-xl">
                      {profile.aboutMe}
                    </p>
                  )}
                </div>

                {/* Share Profile Button */}
                <div className="mt-2 md:mt-0 flex items-center justify-center md:justify-end gap-3">
                  <button
                    onClick={handleSendMessage}
                    className={`bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20 ${
                      isCreatingChat ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                    disabled={isCreatingChat}
                  >
                    {isCreatingChat ? (
                      <>
                        <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                        Creating Chat...
                      </>
                    ) : (
                      <>
                        <Envelope className="mr-2" />
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Stats Grid - Updated to match Profile page */}
              <div className="mt-3 flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-2">
                {/* Level Card */}
                <div className="bg-gradient-to-r from-blue-800/40 to-indigo-800/40 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-500/30 shadow-lg hover:shadow-blue-500/20 transition-all md:w-1/4">
                  <div className="flex items-center justify-center">
                    <LevelUpAlt className="text-blue-400 text-xl pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm">Level</p>
                      <p className="text-white text-2xl font-bold">
                        {profile?.level || 1}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Strength Card */}
                <div className="bg-gradient-to-r from-red-800/40 to-rose-800/40 backdrop-blur-sm rounded-xl p-4 text-center border border-red-500/30 shadow-lg hover:shadow-red-500/20 transition-all md:w-1/4">
                  <div className="flex items-center justify-center">
                    <Dumbbell className="text-red-400 text-xl pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm">Strength</p>
                      <p className="text-white text-2xl font-bold">
                        {profile?.strength || 0}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Intelligence Card */}
                <div className="bg-gradient-to-r from-blue-800/40 to-sky-800/40 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-500/30 shadow-lg hover:shadow-blue-500/20 transition-all md:w-1/4">
                  <div className="flex items-center justify-center">
                    <Brain className="text-blue-400 text-xl pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm">Intelligence</p>
                      <p className="text-white text-2xl font-bold">
                        {profile?.intelligence || 0}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Endurance Card */}
                <div className="bg-gradient-to-r from-green-800/40 to-emerald-800/40 backdrop-blur-sm rounded-xl p-4 text-center border border-green-500/30 shadow-lg hover:shadow-green-500/20 transition-all md:w-1/4">
                  <div className="flex items-center justify-center">
                    <ShieldAlt className="text-green-400 text-xl pl-1 mr-3" />
                    <div className="flex-grow">
                      <p className="text-gray-400 text-sm">Endurance</p>
                      <p className="text-white text-2xl font-bold">
                        {profile?.endurance || 0}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons - Only show for other users' profiles */}
              {!isOwnProfile && (
                <div className="mt-6 flex justify-center md:justify-end space-x-3">
                  <button
                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-green-500/20"
                    onClick={() => setShowTransferModal(true)}
                  >
                    <DollarSign className="mr-2" />
                    <span>Send Money</span>
                  </button>
                  <button
                    className="bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700 text-white px-4 py-2 rounded-xl flex items-center transition-all shadow hover:shadow-lg hover:shadow-yellow-500/20"
                    onClick={() => setShowGiftPremiumModal(true)}
                  >
                    <Crown className="mr-2" />
                    <span>Send Premium</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Other Information - 2 Columns Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* State Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            <div className="flex items-center mb-4">
              <Flag className="text-red-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">State</h2>
            </div>

            {userState ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-900/30 to-rose-900/30 rounded-xl flex items-center justify-center text-2xl text-red-400 border border-red-500/30">
                    {userState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {userState.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {userState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <GlobeAmericas className="mr-2 text-blue-400" />
                      Regions:
                    </span>
                    <span className="text-white">
                      {userState.regionsCount ?? 0}
                    </span>
                  </div>
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <Landmark className="mr-2 text-yellow-400" />
                      Treasury:
                    </span>
                    <span className="text-yellow-400 flex items-center">
                      {userState.treasury?.toLocaleString() || 0}
                      <Coins className="ml-1.5 text-yellow-300" />
                    </span>
                  </div>
                </div>

                <Link
                  to={`/states/${userState.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View State Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  This user doesn't belong to any state.
                </p>
              </div>
            )}
          </div>

          {/* Party Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            <div className="flex items-center mb-4">
              <Users className="text-purple-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">Party</h2>
            </div>

            {profile?.leadingParty ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-900/30 to-violet-900/30 rounded-xl flex items-center justify-center text-2xl text-purple-400 border border-purple-500/30">
                    {profile.leadingParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.leadingParty.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {profile.leadingParty.leader?.username || profile.username}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <UserFa className="mr-2 text-blue-400" />
                      Members:
                    </span>
                    <span className="text-white">
                      {profile.leadingParty.membersCount || 1}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.leadingParty.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Party Details
                </Link>
              </div>
            ) : profile?.memberOfParty ? (
              <div>
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-900/30 to-violet-900/30 rounded-xl flex items-center justify-center text-2xl text-purple-400 border border-purple-500/30">
                    {profile.memberOfParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.memberOfParty.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {profile.memberOfParty.leader?.username || 'Unknown'}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex justify-between bg-gray-700/30 rounded-lg p-3">
                    <span className="text-gray-400 flex items-center">
                      <UserFa className="mr-2 text-blue-400" />
                      Members:
                    </span>
                    <span className="text-white">
                      {profile.memberOfParty.membersCount || 1}
                    </span>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.memberOfParty.id}`}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Party Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  This user doesn't belong to any party.
                </p>
              </div>
            )}
          </div>

          {/* Region Information */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            <div className="flex items-center mb-4">
              <GlobeAmericas className="text-emerald-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">Region</h2>
            </div>

            {region ? (
              <div className="space-y-4">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-900/30 to-teal-900/30 rounded-xl flex items-center justify-center text-2xl text-emerald-400 border border-emerald-500/30">
                    {region.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {region.name}
                    </h3>
                    <p className="text-gray-400">
                      Population:{" "}
                      {region?.population?.toLocaleString() || "N/A"}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="bg-gray-700/30 rounded-lg p-3">
                    <h4 className="text-gray-400 text-sm uppercase mb-2 flex items-center">
                      <Coins className="mr-2 text-yellow-400" />
                      Resources
                    </h4>
                    {region.resources ? (
                      <div className="grid grid-cols-2 gap-2">
                        {Object.entries(region.resources).map(
                          ([key, value]) => (
                            <div
                              key={key}
                              className="flex justify-between bg-gray-800/40 p-2 rounded"
                            >
                              <span className="text-gray-400">{key}:</span>
                              <span className="text-white">
                                {typeof value === "object"
                                  ? value.current
                                  : value}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    ) : (
                      <p className="text-gray-400 text-center">No resources</p>
                    )}
                  </div>
                </div>
                <Link
                  to={`/regions/${region.id}`}
                  className="w-full mt-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-4 py-3 rounded-xl block text-center transition-all shadow hover:shadow-lg hover:shadow-blue-500/20"
                >
                  View Region Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  This user doesn't have a region assigned yet.
                </p>
              </div>
            )}
          </div>

          {/* War Statistics */}
          <div className="bg-gray-800/70 backdrop-blur-sm rounded-xl shadow-xl p-6 mb-6 border border-gray-700/50 hover:border-blue-500/30 transition-all relative overflow-hidden">
            <div className="flex items-center mb-4">
              <ChartLine className="text-amber-400 text-xl mr-2" />
              <h2 className="text-xl font-semibold text-white">
                War Statistics
              </h2>
            </div>
            <UserWarStats userId={id} />
          </div>
        </div>
      </div>
      {showTransferModal && <TransferMoneyModal />}

      {/* Gift Premium Modal */}
      <GiftPremiumModal
        isOpen={showGiftPremiumModal}
        onClose={() => setShowGiftPremiumModal(false)}
        recipientUser={profile}
      />

      {/* Chat Interface Modal */}
      <ChatInterface isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
    </div>
  );
}
