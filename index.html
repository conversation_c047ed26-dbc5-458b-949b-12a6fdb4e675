<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/wn-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Warfront Nations" />
    <link rel="apple-touch-icon" href="/wn-icon.png" />
    
    <!-- Primary Meta Tags -->
    <title>Warfront Nations - Multiplayer Strategy Game | Build Empires & Conquer Territories</title>
    <meta name="title" content="Warfront Nations - Multiplayer Strategy Game | Build Empires & Conquer Territories" />
    <meta name="description" content="Enter a world of strategic warfare, political intrigue, and economic dominance. Build your empire, forge alliances, and conquer territories in this immersive multiplayer strategy game." />
    <meta name="keywords" content="strategy game, multiplayer, war game, empire building, political strategy, online game, browser game, warfront nations" />
    <meta name="author" content="Warfront Nations" />
    <meta name="robots" content="index, follow" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://warfront-nations.com/" />
    <meta property="og:title" content="Warfront Nations - Multiplayer Strategy Game" />
    <meta property="og:description" content="Enter a world of strategic warfare, political intrigue, and economic dominance. Build your empire, forge alliances, and conquer territories." />
    <meta property="og:image" content="https://warfront-nations.com/wn-logo.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Warfront Nations" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://warfront-nations.com/" />
    <meta property="twitter:title" content="Warfront Nations - Multiplayer Strategy Game" />
    <meta property="twitter:description" content="Enter a world of strategic warfare, political intrigue, and economic dominance. Build your empire, forge alliances, and conquer territories." />
    <meta property="twitter:image" content="https://warfront-nations.com/wn-logo.png" />
    
    <!-- Additional SEO Meta Tags -->
    <meta name="theme-color" content="#1f2937" />
    <meta name="msapplication-TileColor" content="#1f2937" />
    <link rel="canonical" href="https://warfront-nations.com/" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://www.googletagmanager.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    
    <!-- Google OAuth -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    
    <!-- Structured Data for Game -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "VideoGame",
      "name": "Warfront Nations",
      "description": "A multiplayer strategy game where players build empires, forge alliances, and conquer territories through strategic warfare and political intrigue.",
      "genre": ["Strategy", "Multiplayer", "War Game", "Political Strategy"],
      "gamePlatform": "Web Browser",
      "applicationCategory": "Game",
      "operatingSystem": "Web Browser",
      "url": "https://warfront-nations.com",
      "image": "https://warfront-nations.com/wn-logo.png",
      "publisher": {
        "@type": "Organization",
        "name": "Warfront Nations"
      },
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      }
    }
    </script>
  </head>
  <!-- Google tag (gtag.js) -->
  <script
    async
    src="https://www.googletagmanager.com/gtag/js?id=G-5JGPPWSE9K"
  ></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());

    gtag("config", "G-5JGPPWSE9K");
  </script>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
