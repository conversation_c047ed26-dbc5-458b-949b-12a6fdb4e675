import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Link } from 'react-router-dom';
import { warService } from '../../services/api/war.service';
import { formatDate } from '../../utils/formatDate';
import { stateService } from '../../services/api/state.service';
import { WarType, WarStatus } from '../../types/war';

const WarsList = () => {
  const [stateWars, setStateWars] = useState([]);
  const [allWars, setAllWars] = useState([]);
  const [myState, setMyState] = useState(null);
  const [activeTab, setActiveTab] = useState('state');
  const [lastCreatedWarId, setLastCreatedWarId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [openDropdown, setOpenDropdown] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [loadedTabs, setLoadedTabs] = useState({ state: false, all: false });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [warCounts, setWarCounts] = useState({
    state: { total: 0, active: 0, pending: 0, ended: 0, sea: 0, ground: 0, revolution: 0 },
    all: { total: 0, active: 0, pending: 0, ended: 0, sea: 0, ground: 0, revolution: 0 }
  });

  // Refs for click outside detection
  const mainDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const typeDropdownRef = useRef(null);

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!openDropdown) return;
      
      let shouldClose = false;
      
      switch (openDropdown) {
        case 'main':
          shouldClose = mainDropdownRef.current && !mainDropdownRef.current.contains(event.target);
          break;
        case 'status':
          shouldClose = statusDropdownRef.current && !statusDropdownRef.current.contains(event.target);
          break;
        case 'type':
          shouldClose = typeDropdownRef.current && !typeDropdownRef.current.contains(event.target);
          break;
        default:
          shouldClose = false;
      }
      
      if (shouldClose) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdown]);

  // Fetch user state
  const fetchStateData = async () => {
    try {
      const stateResponse = await stateService.getUserState();
      setMyState(stateResponse);
      return stateResponse;
    } catch (error) {
      console.error('Error fetching user state:', error);
      setMyState(null);
      return null;
    }
  };

  // Fetch wars for a specific tab
  const fetchWarsForTab = async (tab, page = 1) => {
    setLoading(true);
    setError(null);
    
    try {
      let warData;

      const params = {
        page,
        limit: pagination.limit,
      };

      // Add server-side filters
      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }
      if (typeFilter !== 'all') {
        params.warType = typeFilter;
      }

      if (tab === 'state') {
        const state = myState || await fetchStateData();
        if (state && state.id) {
          warData = await warService.getCurrentStateWars(state.id, params);
          setStateWars(warData.data || warData);
        } else {
          setStateWars([]);
          warData = { data: [], total: 0, page: 1 };
        }
      } else {
        warData = await warService.getAllWars(params);
        setAllWars(warData.data || warData);
      }

      // Update pagination state
      setPagination(prev => ({
        ...prev,
        page: warData.pagination?.page || page,
        total: warData.pagination?.total || 0,
        totalPages: warData.pagination?.totalPages || 0,
        hasNext: warData.pagination?.hasNext || false,
        hasPrev: warData.pagination?.hasPrev || false,
      }));

      // Set filter counts from backend for the specific tab
      if (warData.counts) {
        setWarCounts(prev => ({
          ...prev,
          [tab]: warData.counts
        }));
      }
      setError(null);
      setLastUpdated(new Date());
      setLoadedTabs(prev => ({ ...prev, [tab]: true }));
    } catch (err) {
      console.error(`Error fetching wars for ${tab} tab:`, err);
      setError('Failed to load wars. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Get current wars based on active tab
  const currentWars = useMemo(() => {
    return activeTab === 'state' ? stateWars : allWars;
  }, [activeTab, stateWars, allWars]);

  // Handle pagination
  const handlePageChange = (page) => {
    if (page !== pagination.page && page >= 1 && page <= pagination.totalPages) {
      fetchWarsForTab(activeTab, page);
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    if (filterType === 'status') {
      setStatusFilter(value);
    } else if (filterType === 'type') {
      setTypeFilter(value);
    }
    
    // Reset to page 1
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Watch for filter changes and refetch data
  useEffect(() => {
    if (loadedTabs[activeTab]) {
      fetchWarsForTab(activeTab, 1);
    }
  }, [statusFilter, typeFilter]);

  useEffect(() => {
    // Check for newly created war
    const storedWarId = localStorage.getItem('lastCreatedWarId');
    if (storedWarId) {
      setLastCreatedWarId(storedWarId);
      setActiveTab('state');
      localStorage.removeItem('lastCreatedWarId');
    }

    // Fetch initial data
    const fetchInitialData = async () => {
      const state = await fetchStateData();
      if (state && state.id) {
        fetchWarsForTab('state');
      }
    };

    fetchInitialData();
  }, []);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setPagination(prev => ({ ...prev, page: 1 }));
    if (!loadedTabs[tab]) {
      fetchWarsForTab(tab, 1);
    } else {
      fetchWarsForTab(tab, 1);
    }
  };

  // Get color scheme based on war type
  const getWarTypeColor = (warType) => {
    if (!warType) return {
      border: 'border-purple-500',
      badge: 'bg-gradient-to-r from-purple-600 to-purple-700',
      glow: 'shadow-purple-500/20',
      damageBar: 'bg-gradient-to-r from-purple-500 to-purple-600',
      displayName: 'WAR'
    };

    switch (warType) {
      case WarType.SEA:
        return {
          border: 'border-blue-500',
          badge: 'bg-gradient-to-r from-blue-600 to-blue-700',
          glow: 'shadow-blue-500/20',
          damageBar: 'bg-gradient-to-r from-blue-500 to-blue-600',
          displayName: 'SEA WAR'
        };
      case WarType.GROUND:
        return {
          border: 'border-green-500',
          badge: 'bg-gradient-to-r from-green-600 to-green-700',
          glow: 'shadow-green-500/20',
          damageBar: 'bg-gradient-to-r from-green-500 to-green-600',
          displayName: 'GROUND WAR'
        };
      case WarType.REVOLUTION:
        return {
          border: 'border-yellow-500',
          badge: 'bg-gradient-to-r from-yellow-600 to-yellow-700',
          glow: 'shadow-yellow-500/20',
          damageBar: 'bg-gradient-to-r from-yellow-500 to-yellow-600',
          displayName: 'REVOLUTION'
        };
      default:
        return {
          border: 'border-purple-500',
          badge: 'bg-gradient-to-r from-purple-600 to-purple-700',
          glow: 'shadow-purple-500/20',
          damageBar: 'bg-gradient-to-r from-purple-500 to-purple-600',
          displayName: 'WAR'
        };
    }
  };

  // Get status color class
  const getWarStatusClass = (status) => {
    switch (status) {
      case WarStatus.PENDING:
        return 'bg-gradient-to-r from-blue-600 to-blue-800 text-white';
      case WarStatus.ACTIVE:
        return 'bg-gradient-to-r from-red-600 to-red-800 text-white animate-pulse';
      case WarStatus.ENDED:
        return 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300';
      default:
        return 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300';
    }
  };

  // Render damage indicator
  const renderDamageIndicator = (attackerDamage = 0, defenderDamage = 0) => {
    const totalDamage = attackerDamage + defenderDamage || 1;
    const attackerPercentage = Math.round((attackerDamage / totalDamage) * 100);

    return (
      <div className="relative">
        <div className="flex justify-between text-xs mb-1">
          <span className="font-bold text-gray-400">ATTACKER DMG: {attackerDamage}</span>
          <span className="font-bold text-gray-400">DEFENDER DMG: {defenderDamage}</span>
        </div>
        <div className="w-full bg-gray-700 h-2.5 rounded-full overflow-hidden border border-gray-600">
          <div 
            className="bg-gradient-to-r from-red-700 to-red-900 h-full"
            style={{ width: `${attackerPercentage}%` }}
          ></div>
        </div>
        <div className="absolute inset-0 flex justify-between px-1">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="w-px h-2.5 bg-gray-800"></div>
          ))}
        </div>
      </div>
    );
  };

  // Render wars list
  const renderWarsList = () => {
    if (currentWars.length === 0) {
      return (
        <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-2xl p-8 text-center mt-6">
          <p className="text-xl font-bold text-gray-300 tracking-wider">
            {statusFilter !== 'all' || typeFilter !== 'all' 
              ? `No wars match the selected filters` 
              : 'No wars found in this category.'
            }
          </p>

          {activeTab === 'state' && (!myState || !myState.id) && (
            <Link 
              to="/wars/new" 
              className="mt-6 inline-block bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 px-6 py-3 rounded-xl transition-all font-bold text-white border-2 border-red-800 shadow-lg shadow-red-900/30"
            >
              DECLARE NEW WAR
            </Link>
          )}
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
        {currentWars.map(war => {
          const getWarVisuals = (WarStatus, WarType) => {
            if (war.status === 'ended') {
              return {
                border: 'border-gray-500',
                badge: 'bg-gradient-to-r from-gray-600 to-gray-700',
                glow: 'shadow-gray-500/20',
                damageBar: 'bg-gradient-to-r from-gray-500 to-gray-600',
                displayName: war.displayName,
              };
              }
              return getWarTypeColor(WarType); // assumes this returns the same structure
          };
          const warTypeColor = getWarVisuals(war.status, war.warType);
          return (
            <div
              key={war.id}
              className={`bg-gradient-to-br from-gray-800 to-gray-900/80 p-5 rounded-2xl shadow-lg transition-all duration-300 hover:shadow-xl ${
                war.id === lastCreatedWarId ? 'ring-4 ring-red-500' : ''
              } ${warTypeColor.border} border-l-4 ${war.status === WarStatus.ACTIVE ? warTypeColor.glow + ' shadow-lg' : ''}`}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-extrabold text-white uppercase tracking-wide">
                    {warTypeColor.displayName}
                  </h3>
                  {war.id === lastCreatedWarId && (
                    <span className="inline-block mt-1 bg-red-600 text-white text-xs px-2 py-1 rounded-full">
                      NEW CONFLICT
                    </span>
                  )}
                </div>
                <span className={`px-3 py-1 rounded-lg text-xs font-black uppercase ${warTypeColor.badge}`}>
                  {war.warType?.toUpperCase()?.slice(0, 3) || 'WAR'}
                </span>
              </div>

              <div className="mb-4 text-gray-300 space-y-2">
                <div className="flex justify-between">
                  <span className="font-bold text-gray-400">STATUS:</span>
                  <span className={`px-2 py-1 rounded text-xs font-bold ${getWarStatusClass(war.status)}`}>
                    {war.status.toUpperCase()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-bold text-gray-400">TARGET:</span>
                  <span className="text-white">{war.warTarget ? war.warTarget.toUpperCase() : 'CLASSIFIED'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-bold text-gray-400">STARTED:</span>
                  <span>{formatDate(war.declaredAt)}</span>
                </div>
              </div>

              <div className="mb-4 p-4 bg-gray-800/50 rounded-xl">
                <div className="flex justify-between mb-3">
                  <div className="text-center">
                    <div className="font-bold text-red-400">ATTACKER</div>
                    <div className="text-sm text-white mt-1">
                      {war.attackerRegion ? (
                        <Link 
                          to={`/regions/${war.attackerRegion.id}`} 
                          className="text-neonBlue hover:text-blue-400"
                        >
                          {war.attackerRegion.name}
                        </Link>
                      ) : 'UNKNOWN'}
                      {war.attackerState && <div className="text-xs text-gray-400">{war.attackerState.name}</div>}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-blue-400">DEFENDER</div>
                    <div className="text-sm text-white mt-1">
                      {war.defenderRegion ? (
                        <Link 
                          to={`/regions/${war.defenderRegion.id}`} 
                          className="text-neonBlue hover:text-blue-400"
                        >
                          {war.defenderRegion.name}
                        </Link>
                      ) : 'UNKNOWN'}
                      {war.defenderState && <div className="text-xs text-gray-400">{war.defenderState.name}</div>}
                    </div>
                  </div>
                </div>

                {renderDamageIndicator((war.attackerGroundDamage || war.attackerSeaDamage || 0), (war.defenderGroundDamage || war.defenderSeaDamage || 0) + (war.damageRequirement || 0))}
              </div>

              <div className="mt-4">
                <Link
                  to={`/wars/${war.id}`}
                  className={`block w-full ${warTypeColor.damageBar} hover:opacity-90 py-3 px-4 rounded-xl text-center transition-all font-bold text-white shadow-md`}
                >
                  VIEW BATTLE DETAILS
                </Link>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Refresh current tab
  const handleRefresh = () => {
    fetchWarsForTab(activeTab, pagination.page);
  };

  if (loading && (stateWars.length === 0 && allWars.length === 0)) return (
    <div className="flex flex-col items-center justify-center min-h-[50vh]">
      <div className="relative">
        <div className="w-16 h-16 border-t-2 border-b-2 border-red-600 rounded-full animate-spin"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 bg-red-600 rounded-full animate-ping"></div>
        </div>
      </div>
      <span className="mt-6 text-xl font-bold text-white tracking-wider">
        GATHERING BATTLEFIELD INTELLIGENCE...
      </span>
    </div>
  );

  if (error) return (
    <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-red-100 p-6 rounded-xl mt-8 shadow-lg">
      <div className="flex items-start">
        <svg className="w-8 h-8 text-red-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <div>
          <p className="font-black text-lg">COMMUNICATION FAILURE</p>
          <p className="mt-2">{error}</p>
          <button 
            onClick={handleRefresh}
            className="mt-4 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 px-4 py-2 rounded-md font-bold text-white text-sm"
          >
            RETRY TRANSMISSION
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto p-4 relative">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-6">
        <div className="text-center md:text-left">
          <h1 className="text-3xl md:text-4xl font-black text-white uppercase tracking-wide bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
            WAR COMMAND CENTER
          </h1>
          <p className="text-gray-400 mt-2 font-medium">
            Strategic overview of all active and historical conflicts
            {lastUpdated && (
              <span className="block text-xs text-gray-500 mt-1">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
          <div className="flex flex-col gap-4 w-full">
            <Link
              to="/wars/new"
              className="bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 px-6 py-3 rounded-xl font-bold text-white uppercase tracking-wide transition-all duration-300 border-2 border-red-800 shadow-lg shadow-red-900/30 flex items-center justify-center"
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              NEW WAR
            </Link>

            <button
              onClick={handleRefresh}
              disabled={loading}
              className={`flex items-center justify-center gap-2 px-6 py-3 rounded-xl font-bold transition-all ${
                loading 
                  ? 'bg-gray-800 text-gray-500' 
                  : 'bg-gradient-to-r from-gray-800 to-gray-900 text-white hover:from-gray-700 hover:to-gray-800'
              } border-2 border-gray-700`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              {loading ? 'UPDATING...' : 'REFRESH DATA'}
            </button>
          </div>
        </div>
      </div>

      {lastCreatedWarId && (
        <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-yellow-100 p-4 rounded-xl mb-8 flex items-center">
          <svg className="w-8 h-8 text-yellow-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="font-bold">WAR DECLARATION SUCCESSFUL!</p>
            <p>Your new conflict zone is highlighted below. Prepare for deployment.</p>
          </div>
        </div>
      )}

      <div className="mb-8">
        {/* Main tab filter */}
        <div className="relative mb-4">
          <button
            onClick={() => setOpenDropdown(openDropdown === 'main' ? null : 'main')}
            className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-6 py-3 rounded-xl font-bold text-white border-2 border-gray-700 flex items-center justify-between w-full"
          >
            <div className="flex items-center">
              {activeTab === 'state' && <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>}
              {activeTab === 'all' && <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>}

              <span>
                {activeTab === 'state' ? 'STATE WARS' : 'ALL WARS'}
              </span>
            </div>
            <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {openDropdown === 'main' && (
            <div className="absolute z-10 mt-2 w-full bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl border-2 border-gray-700 overflow-hidden" ref={mainDropdownRef}>
              <button
                className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                  activeTab === 'state' ? 'text-red-400' : 'text-white'
                }`}
                onClick={() => {
                  handleTabChange('state');
                  setOpenDropdown(null);
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                State Wars
                <span className="ml-auto bg-orange-900 text-orange-100 text-xs px-2 py-1 rounded-full">
                  {loadedTabs.state ? warCounts.state.total : '...'}
                </span>
              </button>
              <button
                className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                  activeTab === 'all' ? 'text-red-400' : 'text-white'
                }`}
                onClick={() => {
                  handleTabChange('all');
                  setOpenDropdown(null);
                }}
              >
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                All Wars
                <span className="ml-auto bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                  {loadedTabs.all ? warCounts.all.total : '...'}
                </span>
              </button>
            </div>
          )}
        </div>

        {/* Status and type filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Status filter */}
          <div className="relative">
            <button
              onClick={() => setOpenDropdown(openDropdown === 'status' ? null : 'status')}
              className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-6 py-3 rounded-xl font-bold text-white border-2 border-gray-700 flex items-center justify-between w-full"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span>
                  {statusFilter === 'all' ? 'ALL STATUSES' : 
                   statusFilter === WarStatus.ACTIVE ? 'ACTIVE' : 
                   statusFilter === WarStatus.PENDING ? 'PENDING' : 
                   'ENDED'}
                </span>
              </div>
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {openDropdown === 'status' && (
              <div className="absolute z-10 mt-2 w-full bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl border-2 border-gray-700 overflow-hidden" ref={statusDropdownRef}>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    statusFilter === 'all' ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('status', 'all');
                    setOpenDropdown(null);
                  }}
                >
                  All Statuses
                  <span className="ml-auto bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].total}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    statusFilter === WarStatus.ACTIVE ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('status', WarStatus.ACTIVE);
                    setOpenDropdown(null);
                  }}
                >
                  Active
                  <span className="ml-auto bg-red-900 text-red-100 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].active}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    statusFilter === WarStatus.PENDING ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('status', WarStatus.PENDING);
                    setOpenDropdown(null);
                  }}
                >
                  Pending
                  <span className="ml-auto bg-blue-900 text-blue-100 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].pending}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    statusFilter === WarStatus.ENDED ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('status', WarStatus.ENDED);
                    setOpenDropdown(null);
                  }}
                >
                  Ended
                  <span className="ml-auto bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].ended}
                  </span>
                </button>
              </div>
            )}
          </div>

          {/* Type filter */}
          <div className="relative">
            <button
              onClick={() => setOpenDropdown(openDropdown === 'type' ? null : 'type')}
              className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-6 py-3 rounded-xl font-bold text-white border-2 border-gray-700 flex items-center justify-between w-full"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <span>
                  {typeFilter === 'all' ? 'ALL WAR TYPES' : 
                   typeFilter === 'sea' ? 'SEA' : 
                   typeFilter === 'ground' ? 'GROUND' : 
                   'REVOLUTION'}
                </span>
              </div>
              <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {openDropdown === 'type' && (
              <div className="absolute z-10 mt-2 w-full bg-gradient-to-b from-gray-800 to-gray-900 rounded-xl shadow-2xl border-2 border-gray-700 overflow-hidden" ref={typeDropdownRef}>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    typeFilter === 'all' ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('type', 'all');
                    setOpenDropdown(null);
                  }}
                >
                  All War Types
                  <span className="ml-auto bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].total}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    typeFilter === 'sea' ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('type', 'sea');
                    setOpenDropdown(null);
                  }}
                >
                  Sea Wars
                  <span className="ml-auto bg-blue-900 text-blue-100 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].sea}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    typeFilter === 'ground' ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('type', 'ground');
                    setOpenDropdown(null);
                  }}
                >
                  Ground Wars
                  <span className="ml-auto bg-green-900 text-green-100 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].ground}
                  </span>
                </button>
                <button
                  className={`block w-full text-left px-6 py-3 hover:bg-gray-700/50 font-bold flex items-center ${
                    typeFilter === 'revolution' ? 'text-red-400' : 'text-white'
                  }`}
                  onClick={() => {
                    handleFilterChange('type', 'revolution');
                    setOpenDropdown(null);
                  }}
                >
                  Revolution
                  <span className="ml-auto bg-yellow-900 text-yellow-100 text-xs px-2 py-1 rounded-full">
                    {warCounts[activeTab].revolution}
                  </span>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Filter indicators */}
      <div className="flex justify-between items-center mb-4">
        <div className="text-gray-400">
          Showing <span className="font-bold text-white">{currentWars.length}</span> of <span className="font-bold text-white">{pagination.total}</span> wars
        </div>
        <div className="flex gap-2">
          {statusFilter !== 'all' && (
            <span className="bg-gray-800 text-gray-300 text-xs px-2 py-1 rounded-full">
              Status: {statusFilter.toUpperCase()}
            </span>
          )}
          {typeFilter !== 'all' && (
            <span className="bg-gray-800 text-gray-300 text-xs px-2 py-1 rounded-full">
              Type: {typeFilter.toUpperCase()}
            </span>
          )}
        </div>
      </div>

      {renderWarsList()}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-3 mt-6">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={!pagination.hasPrev || loading}
            className={`px-3 py-2 rounded-lg font-bold transition-all text-sm ${
              !pagination.hasPrev || loading
                ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white'
            }`}
          >
            Previous
          </button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
              const page = Math.max(1, Math.min(pagination.totalPages - 4, pagination.page - 2)) + i;
              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  disabled={loading}
                  className={`px-2 py-2 rounded-lg font-bold transition-all text-sm ${
                    page === pagination.page
                      ? 'bg-gradient-to-r from-red-600 to-red-700 text-white'
                      : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white'
                  }`}
                >
                  {page}
                </button>
              );
            })}
          </div>

          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={!pagination.hasNext || loading}
            className={`px-3 py-2 rounded-lg font-bold transition-all text-sm ${
              !pagination.hasNext || loading
                ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 text-white'
            }`}
          >
            Next
          </button>
        </div>
      )}

      {/* Results Summary */}
      {currentWars.length > 0 && pagination.total > 0 && (
        <div className="text-center mt-4 text-gray-400 text-sm">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} wars
        </div>
      )}
    </div>
  );
};

export default WarsList;
