import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { balanceLogService } from '../../services/api/balance-log.service';
import { formatDate } from '../../utils/formatDate';
import { showErrorToast } from '../../utils/showErrorToast';
import { Coins } from '../svg/svgs';

const BalanceLogsModal = ({ isOpen, onClose }) => {
  const navigate = useNavigate();
  const [balanceLogs, setBalanceLogs] = useState([]);
  const [balanceSummary, setBalanceSummary] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [filters, setFilters] = useState({
    balanceType: '',
    transactionType: '',
    startDate: '',
    endDate: '',
  });
  const [openDropdown, setOpenDropdown] = useState(null);

  // Transaction type labels
  const transactionTypeLabels = {
    gold_purchase: 'Gold Purchase',
    gold_spent: 'Gold Spent',
    gold_earned: 'Gold Earned',
    gold_transfer_sent: 'Gold Transfer Sent',
    gold_transfer_received: 'Gold Transfer Received',
    gold_refund: 'Gold Refund',
    money_earned: 'Money Earned',
    money_spent: 'Money Spent',
    money_transfer_sent: 'Money Transfer Sent',
    money_transfer_received: 'Money Transfer Received',
    money_refund: 'Money Refund',
    system_adjustment: 'System Adjustment',
    premium_subscription: 'Premium Subscription',
    training_cost: 'Training Cost',
    war_cost: 'War Cost',
    factory_work: 'Factory Work',
    party_contribution: 'Party Contribution',
    travel_cost: 'Travel Cost',
    state_leader_wage: 'State Leader Wage',
  };

  // Get color for transaction type
  const getTransactionTypeColor = (transactionType, amount) => {
    const isPositive = amount > 0;
    
    if (transactionType.includes('purchase') || transactionType.includes('earned') || transactionType.includes('received')) {
      return 'text-green-400';
    } else if (transactionType.includes('spent') || transactionType.includes('sent') || transactionType.includes('cost')) {
      return 'text-red-400';
    } else if (transactionType.includes('refund')) {
      return 'text-blue-400';
    } else if (transactionType.includes('adjustment')) {
      return 'text-yellow-400';
    }
    
    return isPositive ? 'text-green-400' : 'text-red-400';
  };

  // Get icon for transaction type
  const getTransactionTypeIcon = (transactionType) => {
    if (transactionType.includes('purchase')) return '🛒';
    if (transactionType.includes('earned')) return '💰';
    if (transactionType.includes('spent')) return '💸';
    if (transactionType.includes('transfer')) return '🔄';
    if (transactionType.includes('refund')) return '↩️';
    if (transactionType.includes('training')) return '🏋️';
    if (transactionType.includes('war')) return '⚔️';
    if (transactionType.includes('factory')) return '🏭';
    if (transactionType.includes('party')) return '👥';
    if (transactionType.includes('premium')) return '👑';
    if (transactionType.includes('adjustment')) return '⚙️';
    if (transactionType.includes('travel')) return '✈️';
    if (transactionType.includes('state_leader_wage')) return '👑';
    return '📊';
  };

  // Fetch balance logs
  const fetchBalanceLogs = async (page = 1) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = {
        page,
        limit: pagination.limit,
        ...filters,
      };

      // Remove empty filters
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key];
        }
      });

      const response = await balanceLogService.getBalanceLogs(params);
      setBalanceLogs(response.data);
      setPagination(response.pagination);
    } catch (err) {
      console.error('Error fetching balance logs:', err);
      setError('Failed to load balance logs. Please try again.');
      showErrorToast('Failed to load balance logs');
    } finally {
      setLoading(false);
    }
  };

  // Fetch balance summary
  const fetchBalanceSummary = async () => {
    try {
      const summary = await balanceLogService.getBalanceSummary();
      setBalanceSummary(summary);
    } catch (err) {
      console.error('Error fetching balance summary:', err);
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({ ...prev, [filterName]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBalanceLogs(1);
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      balanceType: '',
      transactionType: '',
      startDate: '',
      endDate: '',
    });
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchBalanceLogs(1);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    fetchBalanceLogs(page);
  };

  // Refresh data
  const refreshData = () => {
    fetchBalanceLogs(pagination.page);
    fetchBalanceSummary();
  };

  // Render clickable username in money transfer descriptions
  const renderClickableDescription = (log) => {
    if (!log.description) return null;

    // Only for money transfer transactions
    if (
      (log.transactionType === 'money_transfer_sent' || log.transactionType === 'money_transfer_received')
      && (log.metadata?.recipientUsername || log.metadata?.senderUsername)
    ) {
      // Pick the right username and id
      const isSent = log.transactionType === 'money_transfer_sent';
      const username = isSent ? log.metadata?.recipientUsername : log.metadata?.senderUsername;
      const userId = isSent ? log.metadata?.recipientUserId : log.metadata?.senderUserId;

      if (username && userId && log.description.includes(username)) {
        const [before, after] = log.description.split(username);
        return (
          <div className="text-gray-300 text-sm mb-2">
            {before}
            <Link
              to={`/users/${userId}`}
              className="underline font-semibold transition-colors"
            >
              {username}
            </Link>
            {after}
          </div>
        );
      }
    }

    // Fallback
    return <p className="text-gray-300 text-sm mb-2">{log.description}</p>;
  };

  useEffect(() => {
    if (isOpen) {
      fetchBalanceLogs();
      fetchBalanceSummary();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 custom-scrollbar">
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl shadow-2xl border-2 border-gray-700 max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-2xl font-black text-white uppercase tracking-wide bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              TRANSACTION HISTORY
            </h2>
            <p className="text-gray-400 mt-1 font-medium">
              Complete record of all your balance changes
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl font-bold p-2 hover:bg-gray-700 rounded-lg transition-all"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          {loading && balanceLogs.length === 0 ? (
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
              <div className="relative">
                <div className="w-16 h-16 border-t-2 border-b-2 border-blue-600 rounded-full animate-spin"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 bg-blue-600 rounded-full animate-ping"></div>
                </div>
              </div>
              <span className="mt-6 text-xl font-bold text-white tracking-wider">
                LOADING TRANSACTION HISTORY...
              </span>
            </div>
          ) : error ? (
            <div className="bg-gradient-to-r from-red-900/80 to-red-900/60 border-l-4 border-red-500 text-red-100 p-6 rounded-xl shadow-lg">
              <div className="flex items-start">
                <svg className="w-8 h-8 text-red-400 mr-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <p className="font-black text-lg">LOADING ERROR</p>
                  <p className="mt-2">{error}</p>
                  <button 
                    onClick={refreshData}
                    className="mt-4 bg-gradient-to-r from-red-700 to-red-800 hover:from-red-600 hover:to-red-700 px-4 py-2 rounded-md font-bold text-white text-sm"
                  >
                    RETRY
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Balance Summary */}
              {balanceSummary && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4 mb-6">
                  <div className="bg-gradient-to-br from-yellow-800 to-yellow-900/80 border-2 border-yellow-700 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-yellow-300 text-xs font-bold uppercase tracking-wide">Current Gold</p>
                        <p className="text-lg font-black text-white">{balanceSummary.currentGold.toLocaleString()}</p>
                      </div>
                      <div className="text-2xl"><Coins className="text-yellow-400" /></div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-green-800 to-green-900/80 border-2 border-green-700 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-300 text-xs font-bold uppercase tracking-wide">Current Money</p>
                        <p className="text-lg font-black text-white">${balanceSummary.currentMoney.toLocaleString()}</p>
                      </div>
                      <div className="text-2xl">💵</div>
                    </div>
                  </div>

                  {/* <div className="bg-gradient-to-br from-blue-800 to-blue-900/80 border-2 border-blue-700 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-300 text-xs font-bold uppercase tracking-wide">Total Gold Earned</p>
                        <p className="text-lg font-black text-white">{balanceSummary.totalGoldEarned.toLocaleString()}</p>
                      </div>
                      <div className="text-2xl">📈</div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-800 to-purple-900/80 border-2 border-purple-700 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-purple-300 text-xs font-bold uppercase tracking-wide">Total Money Earned</p>
                        <p className="text-lg font-black text-white">${balanceSummary.totalMoneyEarned.toLocaleString()}</p>
                      </div>
                      <div className="text-2xl">📊</div>
                    </div>
                  </div> */}
                </div>
              )}

              {/* Filters */}
              <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-4 mb-6">
                <h3 className="text-lg font-bold text-white mb-3">FILTERS</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                  {/* Balance Type Filter */}
                  <div className="relative">
                    <button
                      onClick={() => setOpenDropdown(openDropdown === 'balanceType' ? null : 'balanceType')}
                      className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-3 py-2 rounded-lg font-bold text-white border-2 border-gray-700 flex items-center justify-between w-full text-sm"
                    >
                      <span>{filters.balanceType || 'All Balance Types'}</span>
                      <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {openDropdown === 'balanceType' && (
                      <div className="absolute z-10 mt-1 w-full bg-gradient-to-b from-gray-800 to-gray-900 rounded-lg shadow-2xl border-2 border-gray-700 overflow-hidden">
                        <button
                          className="block w-full text-left px-3 py-2 hover:bg-gray-700/50 font-bold text-white text-sm"
                          onClick={() => {
                            handleFilterChange('balanceType', '');
                            setOpenDropdown(null);
                          }}
                        >
                          All Balance Types
                        </button>
                        <button
                          className="block w-full text-left px-3 py-2 hover:bg-gray-700/50 font-bold text-white text-sm"
                          onClick={() => {
                            handleFilterChange('balanceType', 'gold');
                            setOpenDropdown(null);
                          }}
                        >
                          Gold
                        </button>
                        <button
                          className="block w-full text-left px-3 py-2 hover:bg-gray-700/50 font-bold text-white text-sm"
                          onClick={() => {
                            handleFilterChange('balanceType', 'money');
                            setOpenDropdown(null);
                          }}
                        >
                          Money
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Transaction Type Filter */}
                  <div className="relative">
                    <button
                      onClick={() => setOpenDropdown(openDropdown === 'transactionType' ? null : 'transactionType')}
                      className="bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-3 py-2 rounded-lg font-bold text-white border-2 border-gray-700 flex items-center justify-between w-full text-sm"
                    >
                      <span>{filters.transactionType ? transactionTypeLabels[filters.transactionType] : 'All Transaction Types'}</span>
                      <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {openDropdown === 'transactionType' && (
                      <div className="absolute z-10 mt-1 w-full max-h-48 overflow-y-auto bg-gradient-to-b from-gray-800 to-gray-900 rounded-lg shadow-2xl border-2 border-gray-700">
                        <button
                          className="block w-full text-left px-3 py-2 hover:bg-gray-700/50 font-bold text-white text-sm"
                          onClick={() => {
                            handleFilterChange('transactionType', '');
                            setOpenDropdown(null);
                          }}
                        >
                          All Transaction Types
                        </button>
                        {Object.entries(transactionTypeLabels).map(([key, label]) => (
                          <button
                            key={key}
                            className="block w-full text-left px-3 py-2 hover:bg-gray-700/50 font-bold text-white text-sm"
                            onClick={() => {
                              handleFilterChange('transactionType', key);
                              setOpenDropdown(null);
                            }}
                          >
                            {label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Start Date Filter */}
                  <div>
                    <input
                      type="date"
                      value={filters.startDate}
                      onChange={(e) => handleFilterChange('startDate', e.target.value)}
                      className="w-full bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-3 py-2 rounded-lg font-bold text-white border-2 border-gray-700 focus:border-blue-500 focus:outline-none text-sm"
                      placeholder="Start Date"
                    />
                  </div>

                  {/* End Date Filter */}
                  <div>
                    <input
                      type="date"
                      value={filters.endDate}
                      onChange={(e) => handleFilterChange('endDate', e.target.value)}
                      className="w-full bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-700 hover:to-gray-800 px-3 py-2 rounded-lg font-bold text-white border-2 border-gray-700 focus:border-blue-500 focus:outline-none text-sm"
                      placeholder="End Date"
                    />
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    onClick={applyFilters}
                    className="bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 px-4 py-2 rounded-lg font-bold text-white transition-all text-sm"
                  >
                    APPLY FILTERS
                  </button>
                  <button
                    onClick={clearFilters}
                    className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 px-4 py-2 rounded-lg font-bold text-white transition-all text-sm"
                  >
                    CLEAR FILTERS
                  </button>
                </div>
              </div>

              {/* Balance Logs */}
              <div className="space-y-3">
                {balanceLogs.map((log) => (
                  <div
                    key={log.id}
                    className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-4 hover:border-gray-600 transition-all"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{getTransactionTypeIcon(log.transactionType)}</div>
                        <div>
                          <h4 className={`text-base font-bold ${getTransactionTypeColor(log.transactionType, log.amount)}`}>
                            {transactionTypeLabels[log.transactionType] || log.transactionType}
                          </h4>
                          <p className="text-gray-400 text-xs">{formatDate(log.createdAt)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-xl font-black ${getTransactionTypeColor(log.transactionType, log.amount)}`}>
                          {log.amount > 0 ? '+' : ''}{log.amount.toLocaleString()}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {log.balanceType === 'gold' ? <Coins className="text-yellow-400 inline-block mr-1" /> : '💵'} {log.balanceAfter.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    {renderClickableDescription(log)}

                    {/* <div className="flex justify-between items-center text-xs text-gray-400">
                      <span>Balance: {log.balanceBefore.toLocaleString()} → {log.balanceAfter.toLocaleString()}</span>
                      {log.metadata && Object.keys(log.metadata).length > 0 && (
                        <span className="text-blue-400">Has additional details</span>
                      )}
                    </div> */}
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex justify-center items-center gap-3 mt-6">
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={!pagination.hasPrev || loading}
                    className={`px-3 py-2 rounded-lg font-bold transition-all text-sm ${
                      !pagination.hasPrev || loading
                        ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 text-white'
                    }`}
                  >
                    Previous
                  </button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const page = Math.max(1, Math.min(pagination.totalPages - 4, pagination.page - 2)) + i;
                      return (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          disabled={loading}
                          className={`px-2 py-2 rounded-lg font-bold transition-all text-sm ${
                            page === pagination.page
                              ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white'
                              : 'bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={!pagination.hasNext || loading}
                    className={`px-3 py-2 rounded-lg font-bold transition-all text-sm ${
                      !pagination.hasNext || loading
                        ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                        : 'bg-gradient-to-r from-blue-700 to-blue-800 hover:from-blue-600 hover:to-blue-700 text-white'
                    }`}
                  >
                    Next
                  </button>
                </div>
              )}

              {/* No Results */}
              {balanceLogs.length === 0 && !loading && (
                <div className="bg-gradient-to-br from-gray-800 to-gray-900/80 border-2 border-gray-700 rounded-xl p-6 text-center">
                  <p className="text-lg font-bold text-gray-300 tracking-wider">
                    No transactions found matching your filters.
                  </p>
                </div>
              )}

              {/* Results Summary */}
              {balanceLogs.length > 0 && (
                <div className="text-center mt-4 text-gray-400 text-sm">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} transactions
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BalanceLogsModal; 