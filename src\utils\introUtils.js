/**
 * Utility functions for managing the new user intro system
 */

export const INTRO_STORAGE_KEY = 'warfront-intro-completed';

/**
 * Check if user has completed the intro
 */
export const hasCompletedIntro = () => {
  return localStorage.getItem(INTRO_STORAGE_KEY) === 'true';
};

/**
 * Mark intro as completed
 */
export const markIntroCompleted = () => {
  localStorage.setItem(INTRO_STORAGE_KEY, 'true');
};

/**
 * Reset intro completion status (useful for testing)
 */
export const resetIntroStatus = () => {
  localStorage.removeItem(INTRO_STORAGE_KEY);
};

/**
 * Check if user is considered "new" (created within specified minutes)
 * @param {Date|string} userCreatedAt - User's creation date
 * @param {number} minutesThreshold - Minutes threshold (default: 10)
 */
export const isNewUser = (userCreatedAt, minutesThreshold = 10) => {
  return true; //testing
  if (!userCreatedAt) return false;
  
  const createdAt = new Date(userCreatedAt);
  const thresholdTime = new Date(Date.now() - minutesThreshold * 60 * 1000);
  
  return createdAt > thresholdTime;
};

/**
 * Check if intro should be shown for a user
 * @param {Object} userData - User data object
 * @param {boolean} forceShow - Force show intro regardless of other conditions
 */
export const shouldShowIntro = (userData, forceShow = true) => {
  if (forceShow) return true;
  if (!userData) return false;
  if (hasCompletedIntro()) return false;
  
  return isNewUser(userData.createdAt);
};

/**
 * Development helper functions (only available in development mode)
 */
if (process.env.NODE_ENV === 'development') {
  // Make functions available globally for console testing
  window.warfrontIntroUtils = {
    hasCompletedIntro,
    markIntroCompleted,
    resetIntroStatus,
    isNewUser,
    shouldShowIntro,
  };
}
