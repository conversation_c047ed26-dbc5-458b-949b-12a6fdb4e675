import { useEffect, useState } from 'react';

export const useOAuth = () => {
  const [isGoogleReady, setIsGoogleReady] = useState(false);

  useEffect(() => {
    // Initialize Google OAuth
    const initGoogle = () => {
      if (window.google && window.google.accounts) {
        // For Google Sign-In button
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: () => {}, // We'll handle this in the component
        });
        setIsGoogleReady(true);
      }
    };

    // Check if scripts are loaded
    const checkGoogle = setInterval(() => {
      if (window.google && window.google.accounts) {
        clearInterval(checkGoogle);
        initGoogle();
      }
    }, 100);

    // Cleanup intervals
    return () => {
      clearInterval(checkGoogle);
    };
  }, []);

  return { isGoogleReady };
}; 