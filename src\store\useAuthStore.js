import { create } from "zustand";


function setCookie(name, value, days = 7) {
  const expires = new Date(Date.now() + days * 864e5).toUTCString();
  let cookie = name + '=' + encodeURIComponent(value) + '; expires=' + expires + '; path=/';
  if (window.location.hostname.endsWith('warfront-nations.com')) {
    cookie += '; domain=.warfront-nations.com; Secure; SameSite=Lax';
  }
  document.cookie = cookie;
}

function getCookie(name) {
  return document.cookie.split('; ').reduce((r, v) => {
    const parts = v.split('=');
    return parts[0] === name ? decodeURIComponent(parts[1]) : r
  }, '');
}


function deleteCookie(name) {
  let cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
  if (window.location.hostname.endsWith('warfront-nations.com')) {
    cookie += '; domain=.warfront-nations.com; Secure; SameSite=Lax';
  }
  document.cookie = cookie;
}

const useAuthStore = create((set) => ({
  user: JSON.parse(localStorage.getItem("user")) || null,
  access_token: localStorage.getItem("access_token") || getCookie("access_token") || null,


  login: (user, access_token) => {
    localStorage.setItem("access_token", access_token);
    localStorage.setItem("user", JSON.stringify(user));
    setCookie("access_token", access_token);
    set({ user: user, access_token });
  },

  logout: () => {
    // Clean up chat connections before logout
    try {
      // Dynamically import to avoid circular dependency
      import('./useChatStore.js').then(({ default: useChatStore }) => {
        const { reset } = useChatStore.getState();
        reset();
      }).catch(error => {
        console.warn('AuthStore: Failed to cleanup chat connections:', error);
      });
    } catch (error) {
      console.warn('AuthStore: Error during chat cleanup:', error);
    }

    localStorage.removeItem("access_token");
    localStorage.removeItem("user");
    deleteCookie("access_token");
    set({ user: null, access_token: null });
  },
}));

export default useAuthStore;
