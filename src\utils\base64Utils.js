/**
 * Base64 utilities for handling JWT tokens with special characters
 */

/**
 * Safely decode base64 string, handling special characters
 * @param {string} str - Base64 encoded string
 * @returns {string} - Decoded string
 */
export const safeBase64Decode = (str) => {
  // Try multiple approaches for robust decoding
  const attempts = [
    // 1. Try original string without padding
    () => atob(str.replace(/=+$/, '')),

    // 2. Try with proper JWT padding
    () => {
      const base = str.replace(/=+$/, '');
      const padded = base + '='.repeat((4 - base.length % 4) % 4);
      return atob(padded);
    },

    // 3. Try original string as-is
    () => atob(str),

    // 4. Try with URL-safe base64 conversion
    () => {
      const urlSafeConverted = str.replace(/-/g, '+').replace(/_/g, '/');
      const base = urlSafeConverted.replace(/=+$/, '');
      const padded = base + '='.repeat((4 - base.length % 4) % 4);
      return atob(padded);
    }
  ];

  for (const attempt of attempts) {
    try {
      return attempt();
    } catch (error) {
      // Continue to next attempt
      continue;
    }
  }

  // If all attempts failed, throw error
  throw new Error('Base64 decode failed');
};

/**
 * Decode JWT payload with proper error handling
 * @param {string} token - JWT token
 * @returns {object|null} - Decoded payload or null if failed
 */
export const decodeJWTPayload = (token) => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error(`Invalid JWT structure: expected 3 parts, got ${parts.length}`);
    }

    const [, payloadBase64] = parts;
    const decodedPayload = safeBase64Decode(payloadBase64);
    const payload = JSON.parse(decodedPayload);

    return payload;
  } catch (error) {
    return null;
  }
};

/**
 * Validate JWT token expiration
 * @param {object} payload - Decoded JWT payload
 * @returns {boolean} - True if token is not expired
 */
export const isTokenNotExpired = (payload) => {
  if (!payload || !payload.exp) {
    return false;
  }
  
  const now = Math.floor(Date.now() / 1000);
  return payload.exp > now;
};


